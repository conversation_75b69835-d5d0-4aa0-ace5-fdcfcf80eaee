package com.foshan.controller.cultureCloud;

import java.math.BigDecimal;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.foshan.form.UploadedFile;
import com.foshan.form.request.UploadReq;
import com.foshan.form.response.upload.UploadRes;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "文化云--上传模块")
@RestController
public class CultureCloudUploadController extends BaseCultureCloudController {

	@ApiOperation(value = "上传直播回看视频(uploadCultureCloudLiveReplay)", httpMethod = "POST", notes = "上传直播回看视频")
	@ResponseBody
	@RequestMapping(value = "/uploadCultureCloudLiveReplay",method = { RequestMethod.POST},produces = MediaType.APPLICATION_JSON_VALUE)
	public UploadRes uploadCultureCloudLiveReplay(HttpServletRequest request, HttpServletResponse response,
			@ModelAttribute UploadedFile uploadedFile, Integer parentAssetId, 
			Integer assetSpecId, Integer serviceId,Integer smallImageWidth
			,Integer smallImageHeight,Integer onShelves,
			String tag,Integer variableFormat,Integer filesize,String accuracy ,Integer middleImageHeight,Integer middleImageWidth ,
								String summaryShort) throws Exception {
		//long start = System.currentTimeMillis();
		request.setCharacterEncoding("utf-8");
		MultipartFile[] multipartFile = uploadedFile.getMultipartFile();
		UploadReq req = new UploadReq();
		req.setAccuracy(StringUtils.isNotEmpty(accuracy) ? new BigDecimal(accuracy) : null);
		req.setAssetSpecId(null!=assetSpecId ? assetSpecId : null );
		req.setFilesize(null!=filesize ? filesize : null);
		req.setImageHeight(null!=smallImageHeight ? smallImageHeight : null);
		req.setImageWidth(null!=smallImageWidth ? smallImageWidth : null);
		req.setMiddleImageHeight(null!=middleImageHeight ? middleImageWidth : null);
		req.setMiddleImageWidth(null!=middleImageWidth ? middleImageWidth : null);
		req.setOnShelves(null!=onShelves ? (1==onShelves ? true :false) : false);
		req.setParentAssetId(null!=parentAssetId ? parentAssetId : null);
		req.setServiceId(null!=serviceId ? serviceId : null);
		req.setSmallImageHeight(null!=smallImageHeight ? smallImageHeight : null);
		req.setSmallImageWidth(null!=smallImageWidth ? smallImageWidth : null);
		req.setTag(StringUtils.isNotEmpty(tag) ? tag : "" );
		req.setVariableFormat(null!=variableFormat ? variableFormat: null);
		req.setSummaryShort(summaryShort);
		UploadRes res =(UploadRes)cultureCloudUploadService.uploadCultureCloudLiveReplay(request,multipartFile,req);
		return res;
	}
}
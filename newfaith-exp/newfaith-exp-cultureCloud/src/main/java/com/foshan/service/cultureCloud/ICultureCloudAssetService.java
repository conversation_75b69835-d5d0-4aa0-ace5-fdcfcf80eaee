package com.foshan.service.cultureCloud;

import javax.servlet.http.HttpServletRequest;

import org.springframework.web.multipart.MultipartFile;

import com.foshan.form.cultureCloud.request.CultureCloudAssetReq;
import com.foshan.form.request.AssetReq;
import com.foshan.form.request.UploadReq;
import com.foshan.form.response.IResponse;
import com.foshan.service.annotation.Audit;

public interface ICultureCloudAssetService {
    public IResponse getCultureCloudAssetList(CultureCloudAssetReq req);
	public IResponse addCultureCloudAsset(CultureCloudAssetReq req);
	public IResponse modifyCultureCloudAsset(CultureCloudAssetReq req);
	public IResponse deleteCultureCloudAsset(CultureCloudAssetReq req);
	public IResponse getCultureCloudAssetInfo(CultureCloudAssetReq req);
	public IResponse auditCultureCloudAsset(CultureCloudAssetReq req);
	public IResponse setAssetState(CultureCloudAssetReq req);
	public IResponse addResources(CultureCloudAssetReq req) ;
	public IResponse modifyResources(CultureCloudAssetReq req);
	public IResponse getResourcesList(AssetReq req);
	public IResponse setBroadcastCount(CultureCloudAssetReq req);
	public IResponse modifyPublishedTime(CultureCloudAssetReq req);
	public IResponse getCultureCloudAssetCommentList(CultureCloudAssetReq req) ;
	public IResponse addCultureCloudAssetComment(CultureCloudAssetReq req);
	public IResponse modifyCultureCloudAssetComment(CultureCloudAssetReq req);
	public IResponse auditCultureCloudAssetComment(CultureCloudAssetReq req);
	public IResponse deleteCultureCloudAssetComment(CultureCloudAssetReq req);
	public IResponse getCultureCloudAssetCommentInfo(CultureCloudAssetReq req);
    public IResponse modifySyncAssetWhgdStatus(CultureCloudAssetReq req);
    public IResponse uploadCultureCloudImage(MultipartFile[] multipartFile,HttpServletRequest request, UploadReq req);

}


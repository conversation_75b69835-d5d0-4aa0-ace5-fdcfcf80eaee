package com.foshan.form.community.response.device;

import com.foshan.form.response.BaseResponse;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="设备表(DeviceRes)")
@JsonInclude(Include.NON_NULL)
public  class AddDeviceRes extends BaseResponse {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "ID",example="1")
	private Integer deviceId;
  @ApiModelProperty(value = "备注")
    private String comment;
    @ApiModelProperty(value = "时间")
    private String createTime;
    @ApiModelProperty(value = "设备地址")
    private String deviceAddress;
    @ApiModelProperty(value = "设备编码")
    private String deviceCode;
    @ApiModelProperty(value = "设备名称")
    private String deviceName;
    @ApiModelProperty(value = "最后修改时间")
    private String lastModifyTime;
    @ApiModelProperty(value = "状态 0--无效数据  1--有效数据",example="1")
    private Integer state;
    @ApiModelProperty(value = "",example="1")
    private Integer messageAccountId;
  
}

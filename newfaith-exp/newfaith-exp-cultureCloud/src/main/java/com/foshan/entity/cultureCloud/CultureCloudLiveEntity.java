package com.foshan.entity.cultureCloud;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_culture_cloud_live")
@org.hibernate.annotations.Table(appliesTo = "t_culture_cloud_live", comment="文化云直播")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CultureCloudLiveEntity extends CultureCloud {
    
    private static final long serialVersionUID = 1L;
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    
    @Column(columnDefinition = "varchar(64) comment '直播UUID'")
    private String liveUUID;
    
    @Column(columnDefinition = "varchar(100) comment '直播名称'")
    private String liveTitle;
    
    @Column(columnDefinition = "varchar(64) comment '直播用户'")
    private String liveCreateUser;
    
    @Column(columnDefinition = "int(1) comment '平台类型 1:本站 2:第三方平台'")
    private Integer platformType;
    
    @Column(columnDefinition = "varchar(500) comment 'PC链接'")
    private String pcUrl;
    
    @Column(columnDefinition = "varchar(500) comment 'H5链接'")
    private String h5Url;
    
    @Column(columnDefinition = "varchar(256) comment '封面图片'")
    private String liveCoverImg;
    
    @Column(columnDefinition = "varchar(200) comment '活动地点'")
    private String liveAddress;
    
    @Column(columnDefinition = "int(11) comment '直播分类ID'")
    private Integer liveCut;
    
    @Column(columnDefinition = "datetime comment '开始时间'")
    private Timestamp liveStartTime;
    
    @Column(columnDefinition = "datetime comment '结束时间'")
    private Timestamp liveEndTime;

    @Column(columnDefinition = "varchar(200) comment '直播回看视频地址'")
    private String liveUploadLink;
    
    @Column(columnDefinition = "int(1) comment '直播来源 2:视频直播 3:第三方流'")
    private Integer cloudService;
    
    @Column(columnDefinition = "varchar(10) comment '发布来源'")
    private String publishSource;
    
    @Column(columnDefinition = "int(11) comment '所属部门ID'")
    private Integer branchDeptId;
    
    @Column(columnDefinition = "int(1) comment '是否转码 0:否 1:是'")
    private Integer liveIsTranscoding;
    
    @Column(columnDefinition = "varchar(100) comment '转码模板ID'")
    private String transcodeTemplateId;
    
    @Column(columnDefinition = "int(1) comment '是否延迟 0:否 1:是'")
    private Integer liveIsDelay;
    
    @Column(columnDefinition = "int(3) comment '延迟时间(秒)'")
    private Integer liveDelayTime;
    
    @Column(columnDefinition = "varchar(500) comment '第三方流地址'")
    private String liveVideoUrl;
    
    @Column(columnDefinition = "varchar(200) comment '固定评论文案'")
    private String fixedCopy;
    
    @Column(columnDefinition = "int(1) comment '是否必须登录 0:否 1:是'")
    private Integer mustLogin;
    
    @Column(columnDefinition = "int(1) comment '是否显示回看 0:否 1:是'")
    private Integer useAutoRecord;
    
    @Column(columnDefinition = "varchar(300) comment '简介'")
    private String liveDesc;
    
    @Column(columnDefinition = "text comment '详情'")
    private String liveIntroduction;
    
    @Column(columnDefinition = "int(1) comment '直播类型'")
    private Integer liveType;
    
    @Column(columnDefinition = "varchar(64) comment '直播应用名称'")
    private String liveAppName;
    
    @Column(columnDefinition = "varchar(64) comment '用户所属区县'")
    private String userCounty;

    @Column(columnDefinition = "int(1) comment '直播状态 0-未审核 1-草稿 2-已审核 3-个人发布活动待审核 5-回收站 6-已发布 7-个人审核未通过' default 0")
    private Integer liveStatus;

    @Column(columnDefinition = "int(1) comment '是否删除 0-删除 1-正常' default 1")
    private Integer isDelete;

    @Column(columnDefinition = "varchar(200) comment '主办单位'")
    private String organizer;

    @Column(columnDefinition = "varchar(200) comment '执行单位'")
    private String executor;

    @Column(columnDefinition = "varchar(200) comment '承办单位'")
    private String undertaker;

    @Column(columnDefinition = "varchar(200) comment '协办单位'")
    private String coOrganizer;

    @ManyToMany(targetEntity = CultureCloudTagEntity.class, fetch = FetchType.LAZY)
    @JoinTable(name = "t_culture_cloud_live_tag", joinColumns = @JoinColumn(name = "liveId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "tagId", referencedColumnName = "id"))
    @JsonIgnore
    private List<CultureCloudTagEntity> tagList = new ArrayList<CultureCloudTagEntity>();
}

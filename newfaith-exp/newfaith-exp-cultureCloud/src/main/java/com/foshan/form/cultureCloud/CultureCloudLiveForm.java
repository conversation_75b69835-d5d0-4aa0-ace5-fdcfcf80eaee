package com.foshan.form.cultureCloud;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="文化云直播对象(CultureCloudLiveForm)")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CultureCloudLiveForm implements IForm {

    private static final long serialVersionUID = -1234567890123456789L;
    
    @ApiModelProperty(value = "直播ID", example = "1")
    private Integer liveId;
    
    @ApiModelProperty(value = "直播标题")
    private String liveTitle;
    
    @ApiModelProperty(value = "直播活动时间状态 1:正在直播 2:尚未开始 3:已结束", example = "1")
    private Integer liveActivityTimeStatus;
    
    @ApiModelProperty(value = "平台类型", example = "1")
    private Integer platformType;
    
    @ApiModelProperty(value = "直播地址")
    private String liveUrl;

    @ApiModelProperty(value = "直播回看视频地址")
    private String liveUploadLink;
    
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;
    
    @ApiModelProperty(value = "直播状态 0-未审核 1-草稿 2-已审核 3-待审核 5-回收站 6-已发布 7-审核未通过", example = "0")
    private Integer liveStatus;
    
    @ApiModelProperty(value = "是否删除 0-删除 1-正常", example = "1")
    private Integer isDelete;

    @ApiModelProperty(value = "直播开始时间")
    private Timestamp liveStartTime;

    @ApiModelProperty(value = "直播结束时间") 
    private Timestamp liveEndTime;

    @ApiModelProperty(value = "直播开始时间字符串")
    private String liveStartTimeStr;

    @ApiModelProperty(value = "直播结束时间字符串")
    private String liveEndTimeStr;

    @ApiModelProperty(value = "直播创建用户")
    private String liveCreateUser;

    @ApiModelProperty(value = "PC端链接")
    private String pcUrl;

    @ApiModelProperty(value = "H5端链接") 
    private  String h5Url;

    @ApiModelProperty(value = "封面图片")
    private String liveCoverImg;

    @ApiModelProperty(value = "活动地点")
    private String liveAddress;


    @ApiModelProperty(value = "直播分类ID")
    private Integer liveCut;

    @ApiModelProperty(value = "艺术类型")
    private String artCut;

    @ApiModelProperty(value = "直播来源 2:视频直播 3:第三方流")
    private Integer cloudService;

    @ApiModelProperty(value = "发布来源")
    private String publishSource;

    @ApiModelProperty(value = "所属部门ID")
    private Integer branchDeptId;

    @ApiModelProperty(value = "是否转码 0:否 1:是")
    private Integer liveIsTranscoding;

    @ApiModelProperty(value = "转码模板ID")
    private String transcodeTemplateId;

    @ApiModelProperty(value = "是否延迟 0:否 1:是")
    private Integer liveIsDelay;

    @ApiModelProperty(value = "延迟时间(秒)")
    private Integer liveDelayTime;

    @ApiModelProperty(value = "第三方流地址")
    private String liveVideoUrl;

    @ApiModelProperty(value = "固定评论文案")
    private String fixedCopy;

    @ApiModelProperty(value = "是否必须登录 0:否 1:是")
    private Integer mustLogin;

    @ApiModelProperty(value = "是否显示回看 0:否 1:是")
    private Integer useAutoRecord;

    @ApiModelProperty(value = "简介")
    private String liveDesc;

    @ApiModelProperty(value = "详情")
    private String liveIntroduction;

    @ApiModelProperty(value = "直播类型")
    private Integer liveType;

    @ApiModelProperty(value = "直播应用名称")
    private String liveAppName;

    @ApiModelProperty(value = "用户所属区县")
    private String userCounty;

    @ApiModelProperty(value = "主办单位")
    private String organizer;

    @ApiModelProperty(value = "执行单位")
    private String executor;

    @ApiModelProperty(value = "承办单位")
    private String undertaker;

    @ApiModelProperty(value = "协办单位")
    private String coOrganizer;

    @ApiModelProperty(value = "艺术类型列表")
    private java.util.List<CultureCloudTypeForm> artTypeList;

    @Override
    public int compareTo(Object o) {
        return 0;
    }
}
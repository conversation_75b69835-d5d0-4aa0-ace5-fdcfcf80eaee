 package com.foshan.service.impl;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Properties;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.transaction.Transactional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

import com.foshan.entity.AssetEntity;
import com.foshan.entity.AssetSpecEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.UploadForm;
import com.foshan.form.request.FileShardReq;
import com.foshan.form.request.UploadReq;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.form.response.upload.UploadRes;
import com.foshan.service.IUploadService;
import com.foshan.service.annotation.Audit;
import com.foshan.util.CodeUtil;
import com.foshan.util.DigestUtil;
import com.foshan.util.PicUtil;

@Transactional
@Service("uploadService")
public class UploadServiceImpl extends GenericService implements IUploadService {



	@SuppressWarnings("rawtypes")
	@Audit(operate = "上传文件")
	@Override
	public IResponse uploadFile(MultipartFile[] multipartFile,HttpServletRequest request, UploadReq req) throws IllegalStateException, IOException {
		UploadRes res = new UploadRes();
        request.setCharacterEncoding("UTF-8");
		req = getAssetSpecForm(req);
		if (null!=multipartFile) {
			for(int i=0; i<multipartFile.length;i++) {
				UploadForm uploadForm = new UploadForm();
				MultipartFile file = multipartFile[i];
				uploadForm=disposeFile(file,req,uploadForm,"");
				res.getUploadFormList().add(uploadForm);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo("图片不能为空！");
			return res;
		}
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	@SuppressWarnings("rawtypes")
	@Audit(operate = "上传图片")
	@Override
	public IResponse uploadImage(MultipartFile[] multipartFile,HttpServletRequest request, UploadReq req) {
		UploadRes res = new UploadRes();
        try {
			request.setCharacterEncoding("UTF-8");
			req = getAssetSpecForm(req);
			if (null!=multipartFile) {
				for(int i=0; i<multipartFile.length;i++) {
					UploadForm uploadForm = new UploadForm();
					MultipartFile file = multipartFile[i];
					uploadForm=disposeFile(file,req,uploadForm,"jpg");
					res.getUploadFormList().add(uploadForm);
				}
			} else {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo("图片不能为空！");
				return res;
			}
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}

		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	
	@SuppressWarnings("unused")
	public UploadForm disposeFile(MultipartFile file,UploadReq req,UploadForm uploadForm,String fileSuffix) throws IOException{
		String dir = "";
		String url = "";
		boolean isPic = false;
		Date date = new Date();
		Integer assetType = null;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
		String today = sdf.format(date);

		String suffix = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".")+1).toLowerCase();
		 if ((suffix.equals("jpg") || suffix.equals("png") || suffix.equals("jpeg") || suffix.equals("bmp")) 
				 &&(StringUtils.isEmpty(fileSuffix) || (StringUtils.isNotEmpty(fileSuffix) && fileSuffix.equals("jpg")))){
			 dir = contextInfo.getAssetFilePath()+ File.separator +"images"+ File.separator + today;
			 url = contextInfo.getAssetFileUrl()+"/"+"images"+ "/" + today + "/";
			 isPic=true;
			 assetType = EntityContext.ASSET_TYPE_IMAGE;
		}else if((suffix.equals("doc") || suffix.equals("docx"))
				&&(StringUtils.isEmpty(fileSuffix) || (StringUtils.isNotEmpty(fileSuffix) && fileSuffix.equals("doc")))){
			dir = contextInfo.getAssetFilePath()+ File.separator +"doc"+ File.separator + today;
			url = contextInfo.getAssetFileUrl()+"/"+"doc"+ "/" + today + "/";
			assetType = EntityContext.ASSET_TYPE_IMAGE;
		}else if((suffix.equals("xls") || suffix.equals("xlsx"))
				&&(StringUtils.isEmpty(fileSuffix) || (StringUtils.isNotEmpty(fileSuffix) && fileSuffix.equals("xls")))){
			dir = contextInfo.getAssetFilePath()+ File.separator +"xls"+ File.separator + today;
			url = contextInfo.getAssetFileUrl()+"/"+"xlsx"+ "/" + today + "/";
			assetType = EntityContext.ASSET_TYPE_IMAGE;
		}else if((suffix.equals("pdf")||suffix.equals("PDF"))
				&&(StringUtils.isEmpty(fileSuffix) || (StringUtils.isNotEmpty(fileSuffix) && fileSuffix.equals("pdf")))){
			dir = contextInfo.getAssetFilePath()+ File.separator +"pdf"+ File.separator + today;
			url = contextInfo.getAssetFileUrl()+"/"+"pdf"+ "/" + today + "/";
			assetType = EntityContext.ASSET_TYPE_IMAGE;
		}else if(suffix.equals("txt")
				&&(StringUtils.isEmpty(fileSuffix) || (StringUtils.isNotEmpty(fileSuffix) && fileSuffix.equals("txt")))){
			dir = contextInfo.getAssetFilePath()+ File.separator +"txt"+ File.separator + today;
			url = contextInfo.getAssetFileUrl()+"/"+"txt"+ "/" + today + "/";
			assetType = EntityContext.ASSET_TYPE_IMAGE;
		}else if((suffix.equals("wmv") || suffix.equals("asf") || suffix.equals("asx") || 
				suffix.equals("rm") || suffix.equals("rmvb") || suffix.equals("mpg") || 
				suffix.equals("mpeg") || suffix.equals("mpe") || suffix.equals("mkv") ||
				suffix.equals("3gp") || suffix.equals("mov") || suffix.equals("flv") ||
				suffix.equals("mp4") || suffix.equals("m4v") || suffix.equals("avi") || 
				suffix.equals("dat") || suffix.equals("")  || suffix.equals("vob"))
				&&(StringUtils.isEmpty(fileSuffix) || (StringUtils.isNotEmpty(fileSuffix) && fileSuffix.equals("mp4")))) {
			dir = contextInfo.getAssetFilePath()+ File.separator +"video"+ File.separator + today;
			url = contextInfo.getAssetFileUrl()+"/"+"video"+ "/" + today + "/";
			assetType = EntityContext.ASSET_TYPE_VIDEO;
		}else if((suffix.equals("mp3") || suffix.equals("MP3"))
				&&(StringUtils.isEmpty(fileSuffix) || (StringUtils.isNotEmpty(fileSuffix) && fileSuffix.equals("mp3")))) {
			dir = contextInfo.getAssetFilePath()+ File.separator +"mp3"+ File.separator + today;
			url = contextInfo.getAssetFileUrl()+"/"+"mp3"+ "/" + today + "/";
			assetType = EntityContext.ASSET_TYPE_SOUND;
		}else{
			return null;
		}



		createMultilayerFile(dir);
		if (file != null) {
			String transName = DigestUtil.getMD5Str(System.currentTimeMillis()+file.getOriginalFilename()+ CodeUtil.getId(10000)) ;
			String fileName = transName+ file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));

			//避免有问题的图片造成机顶盒重启问题
			//modifyImageFormat(fileUpload,fileUpload,suffix);
			AssetEntity asset = null;
			if(null!=req.getAssetId()){
				asset = assetDao.get(req.getAssetId());
			}else{
				asset = new AssetEntity();
			}
			if(isPic){
				//req = getAssetSpecForm(req);
				String newFileName="";
				String formatName="";
				//0:原图格式;1:转成JPG;2:转成PNG;3:转成BMP;4:转成JPEG;5:转成GIF
				if(null!=req.getVariableFormat()){
					if(req.getVariableFormat()==EntityContext.PIC_JPG_FORMAT){
						newFileName=fileName.split("[.]")[0]+".jpg";
						formatName="jpg";
					}else if(req.getVariableFormat()==EntityContext.PIC_PNG_FORMAT){
						newFileName=fileName.split("[.]")[0]+".png";
						formatName="png";
					}else if(req.getVariableFormat()==EntityContext.PIC_BMP_FORMAT){
						newFileName=fileName.split("[.]")[0]+".bmp";
						formatName="bmp";
					}else if(req.getVariableFormat()==EntityContext.PIC_JPEG_FORMAT){
						newFileName=fileName.split("[.]")[0]+".jpeg";
						formatName="jpeg";
					}else if(req.getVariableFormat()==EntityContext.PIC_GIF_FORMAT){
						newFileName=fileName.split("[.]")[0]+".gif";
						formatName="gif";
					}else{
						newFileName=fileName;
						formatName= suffix;
					}
				}else {
					newFileName=fileName;
					formatName= suffix;
				}
				String newFile= dir + File.separator+newFileName;
				//避免有问题的图片造成机顶盒重启问题
				//modifyImageFormat(file.getInputStream(),newFile,formatName);
				if(isOSLinux()){
					Runtime.getRuntime().exec("chmod 644 " + newFile);
				}
				url = url  +newFileName;
				uploadForm.setFilePath(url);
				if(req.isOnShelves()){
					asset.setAssetState(EntityContext.ASSET_STATE_UP);
				}else{
					asset.setAssetState(EntityContext.ASSET_STATE_PENDING);
				}

//				String smallImageName = System.currentTimeMillis() + "-" + MD5Util.MD5(file.getOriginalFilename()) + "-"
//						+ CommonUtil.getId(10000)+formatName;
						//+ file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
				BufferedImage bufferedImage =ImageIO.read(file.getInputStream());
	            Integer width = bufferedImage.getWidth();
	            Integer height = bufferedImage.getHeight();

				if(null!=req.getIsOriginalSize() && req.getIsOriginalSize()==1) {
					PicUtil.resize(file.getInputStream(),  newFile, width, height,true);
				}else if(null!=req.getImageHeight() && null!=req.getImageWidth()){
					PicUtil.resize(file.getInputStream(),  newFile, req.getImageWidth(), req.getImageHeight(),req.isKeepAspectRatio());
				}else{
					String[] imageWidthAndHeight =  contextInfo.getImageWidthAndHeight().split("[*]");
					PicUtil.resize(file.getInputStream(),  newFile, Integer.valueOf(imageWidthAndHeight[0]),
							Integer.valueOf(imageWidthAndHeight[1]),req.isKeepAspectRatio());
				}

				createMultilayerFile(dir+ File.separator+"small");
				String smallImagePath = dir + File.separator+"small"+File.separator+newFileName;
				createMultilayerFile(dir+ File.separator+"middle");
				String middleImagePath = dir + File.separator+"middle"+File.separator+newFileName;

				if(null!=req.getSmallImageWidth() && null!=req.getSmallImageHeight()){
					PicUtil.resize(file.getInputStream(),  smallImagePath, req.getSmallImageWidth(), req.getSmallImageHeight(),req.isKeepAspectRatio());
				}else{
					String[] smallImageWidthAndHeight =  contextInfo.getSmallImageWidthAndHeight().split("[*]");
					PicUtil.resize(file.getInputStream(),  smallImagePath, Integer.valueOf(smallImageWidthAndHeight[0]),
							Integer.valueOf(smallImageWidthAndHeight[1]),req.isKeepAspectRatio());
				}
				
				if(null!=req.getMiddleImageWidth() && null!=req.getMiddleImageHeight()){
					PicUtil.resize(file.getInputStream(), middleImagePath, req.getMiddleImageWidth(), req.getMiddleImageHeight(),req.isKeepAspectRatio());
				}else{
					String[] middleImageWidthAndHeight =  contextInfo.getSmallImageWidthAndHeight().split("[*]");
					PicUtil.resize(file.getInputStream(),  middleImagePath, Integer.valueOf(middleImageWidthAndHeight[0]),
							Integer.valueOf(middleImageWidthAndHeight[1]),req.isKeepAspectRatio());
				}
				
				if(null!=req.getFilesize() 
						&& req.getFilesize()>0 
						&& file.getSize()>req.getFilesize()){
					//设置精度，递归压缩的比率，建议小于0.9,如果为空默认0.9
					if(null!=req.getAccuracy() && req.getAccuracy().compareTo(new BigDecimal(0))==1){
						PicUtil.commpressPicForSize(newFile,newFile,req.getFilesize(), req.getAccuracy().doubleValue());
					}else{
						PicUtil.commpressPicForSize(newFile,newFile, req.getFilesize(),0.9);
					}
				}
				asset.setImageFile(url);
				asset.setSmallImageFile(contextInfo.getAssetFileUrl()+"/"+"images"+ "/" + today + "/"+"small"+ "/" +newFileName);
				asset.setMiddleImageFile(contextInfo.getAssetFileUrl()+"/"+"images"+ "/" + today + "/"+"middle"+ "/" +newFileName);
			}else{
				String fileUpload = dir + File.separator + fileName;
				File tempFile = new File(fileUpload);
				file.transferTo(tempFile);
				if(isOSLinux()){
					Runtime.getRuntime().exec("chmod 644 " + fileUpload);
				}
				url = url + fileName;
				uploadForm.setFilePath(url);
				asset.setAssetState(EntityContext.ASSET_STATE_PENDING);
				asset.setImageFile(url);
				asset.setSmallImageFile(url);
			}

			if(null!=req.getParentAssetId()){
				AssetEntity pae = assetDao.get(req.getParentAssetId());
				if(null!=pae){
					asset.setParentAsset(pae);
					if(isPic){
						asset.setIsCover(EntityContext.IS_NOT_COVER);
					}
				}else if(isPic){
					asset.setIsCover(EntityContext.IS_COVER);
				}
			}else if(isPic){
				asset.setIsCover(EntityContext.IS_COVER);
			}
			asset.setAssetCode(transName);
//			asset.setAssetName(new String(file.getOriginalFilename()
//					.getBytes("ISO-8859-1"),"UTF-8").split("[.]")[0]);
			asset.setAssetName(file.getOriginalFilename());
			asset.setAssetType(assetType);
			asset.setPackageFlag(EntityContext.ASSET_PACKAGE_FLAG_INVALID);
			asset.setSummaryShort(req.getSummaryShort());
			
			assetDao.saveOrUpdate(asset);
			uploadForm.setFileId(asset.getId());
			uploadForm.setFileName(file.getOriginalFilename());
		}
		return uploadForm;
	}
	
	public UploadReq getAssetSpecForm(UploadReq req){
		AssetSpecEntity assetSpec = null;
		if(null != req.getAssetSpecId() || null != req.getServiceId() || StringUtils.isNotEmpty(req.getTag())) {
			StringBuilder hql = new StringBuilder("SELECT a from AssetSpecEntity a where ");
			if(null != req.getAssetSpecId()){
				assetSpec = assetSpecDao.get(req.getAssetSpecId());
				hql.append(" a.id=" + req.getAssetSpecId() +" or");
			}
			if(null != req.getServiceId()){
				hql.append(" a.serviceId=" + req.getServiceId() +" and");
			}
			if(StringUtils.isNotEmpty(req.getTag())){
				hql.append(" a.tag=" + req.getTag());
			}
			List<AssetSpecEntity> list = assetSpecDao.getListByHql(hql.toString().endsWith("or") ?
					hql.toString().substring(0, hql.toString().length()-2) :
						(hql.toString().endsWith("and") ?  hql.toString().substring(0, hql.toString().length()-3) :hql.toString()));
			if(null!=list&&list.size()>0){
				assetSpec = list.get(0);
				req.setAccuracy(null!=assetSpec.getAccuracy() ? assetSpec.getAccuracy() : req.getAccuracy());
				req.setFilesize(null!=assetSpec.getFilesize() ? assetSpec.getFilesize() : req.getFilesize());
				req.setImageHeight(null!=assetSpec.getImageHeight() ? assetSpec.getImageHeight() : req.getImageHeight());
				req.setImageWidth(null!=assetSpec.getImageWidth() ? assetSpec.getImageWidth() : req.getImageWidth());
				req.setOnShelves( assetSpec.isOnShelves() );
				req.setServiceId(null!=assetSpec.getServiceId() ? assetSpec.getServiceId() : req.getServiceId());
				req.setSmallImageHeight(null!=assetSpec.getSmallImageHeight() ? assetSpec.getSmallImageHeight() : req.getSmallImageHeight());
				req.setSmallImageWidth(null!=assetSpec.getSmallImageWidth() ? assetSpec.getSmallImageWidth() : req.getSmallImageWidth());
				
				req.setMiddleImageHeight(null!=assetSpec.getMiddleImageHeight() ? assetSpec.getMiddleImageHeight() : req.getMiddleImageHeight());
				req.setMiddleImageWidth(null!=assetSpec.getMiddleImageWidth() ? assetSpec.getMiddleImageWidth() : req.getMiddleImageWidth());
				//req.setTag( StringUtils.isNotEmpty(assetSpec.getTag()) ? assetSpec.getTag() : req.getTag());
				req.setAssetSpecId(assetSpec.getId());
				req.setVariableFormat(null!=assetSpec.getVariableFormat() ? assetSpec.getVariableFormat() : req.getVariableFormat());
				req.setKeepAspectRatio(assetSpec.isKeepAspectRatio());
				req.setKeepAspectRatio(assetSpec.isKeepAspectRatio());
			}
		}
		
		req.setImageHeight(null!=req.getImageHeight() ? req.getImageHeight() : 
			Integer.valueOf(contextInfo.getImageWidthAndHeight().split("\\*")[1]));
		req.setImageWidth(null!=req.getImageWidth() ? req.getImageWidth() :
			Integer.valueOf(contextInfo.getImageWidthAndHeight().split("\\*")[0]));
		
		req.setSmallImageHeight(null!=req.getSmallImageHeight() ? req.getSmallImageHeight() : 
			Integer.valueOf(contextInfo.getSmallImageWidthAndHeight().split("\\*")[1]));
		req.setSmallImageWidth(null!=req.getSmallImageWidth() ? req.getSmallImageWidth() : 
			Integer.valueOf(contextInfo.getSmallImageWidthAndHeight().split("\\*")[0]));
		req.setMiddleImageHeight(null!=req.getMiddleImageHeight() ? req.getMiddleImageHeight() : 
			Integer.valueOf(contextInfo.getMiddleImageWidthAndHeight().split("\\*")[1]));
		req.setMiddleImageWidth(null!=req.getMiddleImageWidth() ? req.getMiddleImageWidth() : 
			Integer.valueOf(contextInfo.getMiddleImageWidthAndHeight().split("\\*")[0]));
		return req;
	}
	
	/*
	 * 
	 * realUrl    : 文件真实地址
	 * virtualUrl :　虚拟地址
	 * saveState  : true需要存到t_asset表
	 * assetType  :　当saveState＝true时要填写，媒类型　0--图片 1--声音 2--视频
	 * fileSuffix :　文件后缀，如多个以逗号隔开
	 */
	@Audit(operate = "上传文件")
	@SuppressWarnings("rawtypes")
	public IResponse uploadFile(HttpServletRequest request,String realUrl ,
			String virtualUrl, boolean saveState,Integer assetType,String fileSuffix)throws IllegalStateException, IOException {

		UploadRes res = new UploadRes();
		UploadForm uploadForm = new UploadForm();
		CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(
				request.getSession().getServletContext());
		if (multipartResolver.isMultipart(request)) {

			MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
			Iterator iter = multiRequest.getFileNames();
			while (iter.hasNext()) {
				MultipartFile file = multiRequest.getFile(iter.next().toString());
				String suffix = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".")+1).toLowerCase();

				if(!fileSuffix.contains(suffix)){
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO + "不支持该文件类型");
					return res;
				}
				
				createMultilayerFile(realUrl);
				if (file != null) {
					String fileName = System.currentTimeMillis() + "-" + DigestUtil.getMD5Str(file.getOriginalFilename()) + "-"
							+ CodeUtil.getId(10000)
							+ file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
					String fileUpload = realUrl + File.separator + fileName;
					file.transferTo(new File(fileUpload));
					if (suffix.equals("jpg") || suffix.equals("png") || suffix.equals("jpeg") || suffix.equals("bmp") || suffix.equals("gif")){
						modifyImageFormat(fileUpload,fileUpload,suffix);
					}
					String url =  virtualUrl+ "/"  + fileName;
					uploadForm.setFilePath(url);
					uploadForm.setFileName(fileName);
					if(saveState){
						AssetEntity asset = new AssetEntity();
						String transName = String.valueOf(DigestUtil.getMD5Str(fileName));
						asset.setAssetCode(transName);
						asset.setAssetName(new String(file.getOriginalFilename().getBytes("ISO-8859-1"),"UTF-8"));
						asset.setAssetState(EntityContext.ASSET_STATE_PENDING);
						asset.setAssetType(assetType);
						asset.setImageFile(url);
						asset.setPackageFlag(EntityContext.ASSET_PACKAGE_FLAG_INVALID);
						asset.setSmallImageFile(url);
						assetDao.save(asset);
						uploadForm.setFileId(asset.getId());
					}
				}
			}
			//res.setUploadFile(f);
			res.getUploadFormList().add(uploadForm);
		} 
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	protected static boolean createMultilayerFile(String dir) {
		try {
			File dirPath = new File(dir);
			if (!dirPath.exists()) {
				dirPath.mkdirs();
			}
		} catch (Exception e) {
			System.out.println("创建多层目录操作出错: " + e.getMessage());
			e.printStackTrace();
			return false;
		}
		return true;
	}

	@SuppressWarnings("rawtypes")
	@Audit(operate = "上传文件")
	@Override
	public IResponse uploadFile(HttpServletRequest request, MultipartFile[] file,UploadReq req) {
		UploadRes res = new UploadRes();
		req = getAssetSpecForm(req);
		for (MultipartFile tempFile : file) {
			try {
				UploadForm uploadForm = new UploadForm();			
				uploadForm=disposeFile(tempFile,req,uploadForm,"");
				res.getUploadFormList().add(uploadForm);
			} catch (IOException e) {
				res.setRet("0003");
				res.setRetInfo("服务器异常！！！");
				e.printStackTrace();
				return res;
			}
		}
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	
	@Audit(operate = "上传文件")
	public String uploadFile(MultipartFile file, HttpServletRequest request,String dir) throws IOException {
		String fileName = System.currentTimeMillis() + "-" + DigestUtil.getMD5Str(file.getOriginalFilename()) + "-"
				+ CodeUtil.getId(10000)
				+ file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
		String fileUpload = dir  ;
		file.transferTo(new File(fileUpload+"/"+fileName));
		String suffix = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".")+1).toLowerCase();
		if (suffix.equals("jpg") || suffix.equals("png") || suffix.equals("jpeg") || suffix.equals("bmp") || suffix.equals("gif")){
			modifyImageFormat(fileUpload+"/"+fileName,fileUpload+"/"+fileName,suffix);
		}
		if(isOSLinux()){
			Runtime.getRuntime().exec("chmod 644 " + fileUpload+fileName);
		}
		
		return fileName;
	}
	public  boolean isOSLinux() {
		Properties prop = System.getProperties();
		String os = prop.getProperty("os.name");
		boolean statu = false;
		if (os != null && os.toLowerCase().indexOf("linux") > -1) {
			statu=true;
		} 
		System.out.println("-----------------isOSLinux:"+statu+";name:"+os.toLowerCase().indexOf("linux"));
		return statu;
	}
	
	/**
	 * 修改原图的文件格式
	 * @param srcPath 原图路径
	 * @param destPath 新图路径
	 * @param formatName 图片格式，支持bmp|gif|jpg|jpeg|png
	 * @return
	 */
	@Audit(operate = "修改原图的文件格式")
	public static boolean modifyImageFormat(String srcPath, String destPath, String formatName) {
		boolean isSuccess = false;
		InputStream fis = null;
		try {
			fis = new FileInputStream(srcPath); 
			BufferedImage bufferedImg = ImageIO.read(fis);
			isSuccess = ImageIO.write(bufferedImg, formatName, new File(destPath));
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			if (fis != null) {
				try {
					fis.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return isSuccess;
	}


	@Override
	@Audit(operate = "删除图片")
	public IResponse deleteImage(UploadReq req) {
		UploadRes res = new UploadRes();
		Integer assetId = (null != req.getAssetId()) ? req.getAssetId() : req.getKey();
		
		if(null != assetId){
			assetDao.deleteById(assetId);
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		}else{
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	public IResponse uploadShard(FileShardReq req) {
		UploadRes res = new UploadRes();
		if(StringUtils.isNotEmpty(req.getKey()) && StringUtils.isNotEmpty(req.getFileSuffix()) && StringUtils.isNotEmpty(req.getShard()) 
				&& null!=req.getShardIndex() && null != req.getShardTotal()
				&& null != req.getShardSize()) {
			String key = req.getKey();
	        String suffix = req.getFileSuffix();
	        String shardBase64 = req.getShard();
	        MultipartFile shard = Base64ToMultipartFile.base64ToMultipart(shardBase64);

	        // 保存文件到本地
	        Date date = new Date();
	        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd/");
	        String format = simpleDateFormat.format(date);
	        String fullPath ="";
	        String url = "";
	        Integer assetType = EntityContext.ASSET_TYPE_DOC;
			if ((suffix.equals("jpg") || suffix.equals("png") || suffix.equals("jpeg") || suffix.equals("bmp") || suffix.equals("gif"))){
				fullPath = "images"+ File.separator + format;
				assetType = EntityContext.ASSET_TYPE_IMAGE;
			}else if(suffix.equals("doc") || suffix.equals("docx")){
				fullPath = "doc"+ File.separator + format;
			}else if(suffix.equals("xls") || suffix.equals("xlsx")){
				fullPath = "xls"+ File.separator + format;
			}else if(suffix.equals("pdf")||suffix.equals("PDF")){
				fullPath = "pdf"+ File.separator + format;
			}else if(suffix.equals("txt")){
				fullPath = "txt"+ File.separator + format;
			}else if(suffix.equals("wmv") || suffix.equals("asf") || suffix.equals("asx") || 
					suffix.equals("rm") || suffix.equals("rmvb") || suffix.equals("mpg") || 
					suffix.equals("mpeg") || suffix.equals("mpe") || suffix.equals("mkv") ||
					suffix.equals("3gp") || suffix.equals("mov") || suffix.equals("flv") ||
					suffix.equals("mp4") || suffix.equals("m4v") || suffix.equals("avi") || 
					suffix.equals("dat") || suffix.equals("")  || suffix.equals("vob")) {
				fullPath ="video"+ File.separator + format;
				assetType = EntityContext.ASSET_TYPE_VIDEO;
			}else{
				fullPath =   format;
			}
	        if (!new File(contextInfo.getAssetFilePath() + File.separator +fullPath).exists()){
	            new File(contextInfo.getAssetFilePath() + File.separator +fullPath).mkdirs();
	        }
	        url = fullPath;
	        String path = contextInfo.getAssetFilePath() + File.separator + new StringBuffer(fullPath)
	        		.append(key)
	                .append(".")
	                .append(suffix)
	                .toString(); 
	        url = contextInfo.getAssetFileUrl() + "/" + new StringBuffer(fullPath)
	        		.append(key)
	                .append(".")
	                .append(suffix)
	                .toString(); 
	        String localPath = new StringBuffer(path)
	                .append(".")
	                .append(req.getShardIndex())
	                .toString(); 
	        File dest = new File(localPath);
	        try {
				shard.transferTo(dest);
			} catch (IllegalStateException e1) {
				e1.printStackTrace();
			} catch (IOException e1) {
				e1.printStackTrace();
			}
	        // 合并分片
	        if (req.getShardIndex().equals(req.getShardTotal())) {
	            try {
					res.getUploadFormList().add(merge(req,path,url,assetType));
				} catch (Exception e) {
					e.printStackTrace();
				}
	        }
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		}else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

        return res;
	}

	public UploadForm merge(FileShardReq req,String path,String url,Integer assetType) throws Exception {
		File newFile = new File(path);
        FileOutputStream outputStream = new FileOutputStream(newFile, true);//文件追加写入
        FileInputStream fileInputStream = null;//分片文件
        byte[] byt = new byte[req.getShardSize()];
        int len;
        try {
            for (int i = 0; i < req.getShardTotal(); i++) {
                // 读取第i个分片
                fileInputStream = new FileInputStream(new File( path + "." + (i + 1))); 
                while ((len = fileInputStream.read(byt)) != -1) {
                    outputStream.write(byt, 0, len);
                }
            }
        } catch (IOException e) {
        	e.printStackTrace();
        } finally {
            try {
                if (fileInputStream != null) {
                    fileInputStream.close();
                }
                outputStream.close();
            } catch (Exception e) {
            	e.printStackTrace();
            }
        }
        File file = null;
        String filePath = "";
        for (int i = 0; i < req.getShardTotal(); i++) {
            filePath =  path + "." + (i + 1);
            file = new File(filePath);
            file.delete();
            System.gc();
        }
        AssetEntity asset = new AssetEntity();
        asset.setAssetState(EntityContext.ASSET_STATE_UP);
        asset.setImageFile(url);
        asset.setIsCover(EntityContext.IS_NOT_COVER);
		asset.setAssetCode(req.getKey());
		asset.setAssetName(req.getFileName());
		asset.setAssetType(assetType);
		asset.setPackageFlag(EntityContext.ASSET_PACKAGE_FLAG_INVALID);
		asset.setAssetOrders(0);
		asset.setBroadcastCount(0);
		assetDao.save(asset);
		UploadForm uploadForm = new UploadForm();
		uploadForm.setFileId(asset.getId());
		uploadForm.setFileName(asset.getAssetName());
		uploadForm.setFilePath(url);
		
		return uploadForm;
    }

}

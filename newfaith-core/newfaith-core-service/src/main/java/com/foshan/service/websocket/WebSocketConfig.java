package com.foshan.service.websocket;

import javax.websocket.HandshakeResponse;
import javax.websocket.server.HandshakeRequest;
import javax.websocket.server.ServerEndpointConfig;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;

@Configuration
public class WebSocketConfig extends ServerEndpointConfig.Configurator {

    @Bean
    public ServerEndpointExporter serverEndpointExporter() {
        return new ServerEndpointExporter();
    }
    
    
   	@Override
   public void modifyHandshake(ServerEndpointConfig sec, HandshakeRequest request, HandshakeResponse response) {
       super.modifyHandshake(sec, request, response);
       // 在这里添加你的自定义逻辑，比如修改响应头等
       //sec.getUserProperties().put("user", getPrincipal(false));
   }
   
/**
   	 *  查询当前登录用户的实体
   	 * @param returnFullEntity 是否返回完整entity，true返回完整的entity，false返回shire中存的entity。shiro缓存了id、电话、微信openid，设置为false时，只有这三个数据。
   	 * @return PlatformUserEntity或AccountEntity的object，使用的时候进行强转 用户没登录或rememberme
   	 *         cookie过期则返回null
   	 *         
   	 *         ******** modify by Genie,直接返回principal,会话状态失效者返回null。 
   	
   	protected Object getPrincipal(boolean returnFullEntity){
   		Subject curUser = SecurityUtils.getSubject();
   		PrincipalCollection principals = curUser.getPrincipals();
   		if(null!=principals&&!principals.isEmpty()){
   			@SuppressWarnings("unchecked")
   			List<Object> principalList = principals.asList();
   			return principalList.get(1);
   		}else{
   			return null;
   		}

   	} **/

}
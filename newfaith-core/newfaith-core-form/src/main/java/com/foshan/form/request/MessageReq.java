package com.foshan.form.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="媒资请求参数(AssetReq)")
public class MessageReq extends BasePageRequest {
	/**
	 *
	 /**
	 *  消息FORM
	 */
	@ApiModelProperty(value = "消息ID",example="1")
	private Integer id;
	@ApiModelProperty(value = "消息编号")
	private String messageCode;
	@ApiModelProperty(value = "发送者ID",example="1")
	private Integer senderId;
	@ApiModelProperty(value = "接收者ID")
	private Integer receiverId;
	@ApiModelProperty(value = "接收者类型:0-member,1-user")
	private Integer receiverType;
	@ApiModelProperty(value = "消息正文")
	private String content;
	@ApiModelProperty(value = "消息类型:0-系统消息,1-装修管理类,2-事项申请类,3-催缴费类,4-服务消息")
	private Integer type;
	@ApiModelProperty(value = "PC跳转链接")
	private String url;
	@ApiModelProperty(value = "手机跳转链接")
	private String phoneUrl;
	@ApiModelProperty(value = "TV跳转链接")
	private String tvUrl;
	@ApiModelProperty(value = "消息状态：0-未读,1-已读")
	private Integer status;
	@ApiModelProperty(value = "发送时间")
	private String createTime;
	@ApiModelProperty(value = "阅读时间")
	private String readTime;
	@ApiModelProperty(value = "最后修改时间")
	private String lastModifyTime;
	@ApiModelProperty(value = "数据状态 0--无效数据  1--有效数据")
	private Integer state;
	@ApiModelProperty(value = "多个接收者ID")
	private List<Integer> receiverIds = new ArrayList<Integer>();
	@ApiModelProperty(value = "开始时间")
	private String startTime;
	@ApiModelProperty(value = "结束时间")
	private String endTime;

	/**
	 *
	 * @param receiverType 接收者类型:0-member,1-user
	 * @param receiverId  接收者ID:userId 或 accountId
	 * @param content 消息正文
	 * @param url 跳转链接
	 */
	public MessageReq(Integer receiverType, Integer receiverId, String content, String url) {
		this.receiverType = receiverType;
		this.receiverId = receiverId;
		this.content = content;
		this.url = url;
	}


	/**
	 *
	 * @param receiverType 接收者类型:0-member,1-user
	 * @param receiverId  接收者ID:userId 或 accountId
	 * @param content 消息正文
	 * @param url 跳转链接
	 * @param phoneUrl 手机跳转链接
	 * @param tvUrl 电视端跳转链接
	 */
	public MessageReq(Integer receiverType, Integer receiverId, String content, String url,String phoneUrl,String tvUrl) {
		this.receiverType = receiverType;
		this.receiverId = receiverId;
		this.content = content;
		this.url = url;
		this.phoneUrl = phoneUrl;
		this.tvUrl = tvUrl;
	}

	/**
	 *
	 * @param receiverType 接收者类型:0-member,1-user
	 * @param receiverId  接收者ID:userId 或 accountId
	 * @param type  消息类型  消息类型:0-系统消息,1-装修管理类,2-事项申请类,3-催缴费类,4-服务消息
	 * @param content 消息正文
	 * @param url 跳转链接
	 */
	public MessageReq(Integer receiverType, Integer receiverId,Integer type, String content, String url) {
		this.receiverType = receiverType;
		this.receiverId = receiverId;
		this.type = type;
		this.content = content;
		this.url = url;
	}

	/**
	 *
	 * @param receiverType 接收者类型:0-member,1-user
	 * @param receiverId  接收者ID:userId 或 accountId
	 * @param type  消息类型  消息类型:0-系统消息,1-装修管理类,2-事项申请类,3-催缴费类,4-服务消息
	 * @param content 消息正文
	 * @param url 跳转链接
	 * @param phoneUrl 手机跳转链接
	 * @param tvUrl 电视端跳转链接
	 */
	public MessageReq(Integer receiverType, Integer receiverId,Integer type, String content, String url,String phoneUrl,String tvUrl) {
		this.receiverType = receiverType;
		this.receiverId = receiverId;
		this.type = type;
		this.content = content;
		this.url = url;
		this.phoneUrl = phoneUrl;
		this.tvUrl = tvUrl;
	}

	/**
	 *
	 * @param receiverType 接收者类型:0-member,1-user
	 * @param receiverIds  接收者List<Integer>:元素为ID:userId 或 accountId
	 * @param type  消息类型  消息类型:0-系统消息,1-装修管理类,2-事项申请类,3-催缴费类,4-服务消息
	 * @param content 消息正文
	 * @param url 跳转链接
	 * @param phoneUrl 手机跳转链接
	 * @param tvUrl 电视端跳转链接
	 */
	public MessageReq(Integer receiverType, List<Integer> receiverIds,Integer type, String content, String url,String phoneUrl,String tvUrl) {
		this.receiverType = receiverType;
		this.receiverIds = receiverIds;
		this.type = type;
		this.content = content;
		this.url = url;
		this.phoneUrl = phoneUrl;
		this.tvUrl = tvUrl;
	}
}

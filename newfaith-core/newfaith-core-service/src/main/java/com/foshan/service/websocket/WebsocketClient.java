package com.foshan.service.websocket;

import java.io.IOException;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import javax.websocket.OnClose;
import javax.websocket.OnError;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@ServerEndpoint(value="/websocketClient/{clientCode}/{clientType}",configurator = WebSocketConfig.class)
public class WebsocketClient {
    private static Logger logger = LoggerFactory.getLogger(WebsocketClient.class);
    private static int onlineCount = 0;
	private static final int MAX_ONLINE_COUNT = 1200;
    private  String clientCode;
    //0：PC端；1：手机端；2：电视大屏
    private  Integer clientType;
    //private static Map<String, WebsocketClient> clientMap = new ConcurrentHashMap<String, WebsocketClient>();
    //private static Map<Integer, List<String>> clientTypeMap = new ConcurrentHashMap<Integer, List<String>>();
	private static Map<Integer, Map<String,WebsocketClient>> clientTypeMap = new ConcurrentHashMap<Integer, Map<String, WebsocketClient>>();
	private Session session;
    
    
    //连接时执行
    @OnOpen
    public void onOpen(Session session,@PathParam("clientCode") String clientCode,@PathParam("clientType") Integer terminalType) throws IOException{
//		Object user = session.getUserProperties().get("user");
//		if(user == null) {
//			logger.info("用户没有登录，新建ws链接失败！");
//			session.close();
//			return;
//		}
//		logger.info("user:{}",user.toString());
    	if(getOnlineCount() > MAX_ONLINE_COUNT){
			logger.info("当前在线人数超过限制数:{}!!",MAX_ONLINE_COUNT);
			session.close();
			return;
		}
		if(terminalType == null || StringUtils.isEmpty(clientCode) || clientCode.length() < 3){
			logger.info("新连接失败，缺少终端用户信息或渠道信息：{}",clientCode);
			session.close();
			return;
		}
		this.clientCode = clientCode;
		this.clientType = terminalType;
		this.session = session;
		Map<String, WebsocketClient> clientMap =  clientTypeMap.get(terminalType);
		if(clientMap != null){
			if(clientMap.get(this.clientCode) == null){
				addOnlineCount();
			}
			clientMap.put(clientCode,this);
		}
		else{
			clientMap = new ConcurrentHashMap<String,WebsocketClient>();
			clientMap.put(clientCode,this);
			clientTypeMap.put(terminalType,clientMap);
			addOnlineCount();
		}
		logger.info("新连接：{}",clientCode);
		logger.info("总链数：{}",getOnlineCount());
	}
    
    //关闭时执行
    @OnClose
    public void onClose(Session session){
		logger.info("连接关闭：{} ",this.clientCode);
		WebsocketClient websocketClient = clientTypeMap.get(this.clientType).get(this.clientCode);
		if(websocketClient != null) {
			Session sessionInMap = websocketClient.session;
			if (sessionInMap.getId().equals(session.getId())) {
				clientTypeMap.get(this.clientType).remove(this.clientCode);
				// System.out.println(session.getId());
				subOnlineCount();
			}
			logger.info("总链数：{}", getOnlineCount());
		}
		
		
    }
    
    //收到消息时执行
    @OnMessage
    public void onMessage(String message, Session session) throws IOException {
//        logger.debug("收到用户{}的消息{}",this.smartcardId,message);
//        session.getBasicRemote().sendText("收到 "+this.smartcardId+" 的消息 "); //回复用户
//        sendMessageAll(this.clientCode+":"+message);
    }
    
    //连接错误时执行
    @OnError
    public void onError(Session session, Throwable error) throws IOException {
        logger.info("用户id为：{}的连接发生错误：{}",this.clientCode, error.getMessage());
		if(session.isOpen()){
			session.close();
		}
    }

	/**
	 * 针对单个终端发送消息
	 * @param message String 消息内容
	 * @param clientCode  String 终端编号，如果clientType
	 * @param clientType Integer 终端渠道类型 0：PC端；1：手机端；2：电视大屏；3：驾驶舱大屏  如果为null时所有渠道的clientCode都会发
	 * @throws IOException
	 */
    public static void sendMessageTo(String message, String clientCode, Integer clientType) throws IOException {
		if(clientType == null){
			sendMessageTo(message,clientCode);
		}
		else{
			Map<String, WebsocketClient> clientMap  = clientTypeMap.get(clientType);
			if(clientMap != null) {
				WebsocketClient item = clientMap.get(clientCode);
				if(item != null && null != item.session) {
					item.session.getAsyncRemote().sendText(message);
					logger.debug("向{}/{}发送消息：{}", clientCode,clientType,message);
					logger.info("向{}/{}发送消息", clientCode,clientType);
				}
			}
		}
    }

	/**
	 * 针对单个终端发送消息
	 * @param message String 消息内容
	 * @param clientCode  String 终端用户编号，全局要唯一
	 * @throws IOException
	 */
	public static void sendMessageTo(String message, String clientCode) throws IOException {
		for(Map<String, WebsocketClient> clientMap:clientTypeMap.values()){
			if(clientMap != null) {
				WebsocketClient item = clientMap.get(clientCode);
				if (item != null && null != item.session) {
					item.session.getAsyncRemote().sendText(message);
					logger.debug("向{}/{}发送消息：{}", clientCode,"*",message);
					logger.info("向{}/{}发送消息", clientCode,"*");
				}
			}
		}
	}


	/**
	 * 针对某个渠道所有终端用户发送消息
	 * @param message String 消息内容
	 * @param clientType  Integer 终端渠道类型 0：PC端；1：手机端；2：电视大屏；3：驾驶舱大屏
	 * @throws IOException
	 */
	public static void sendMessageByClientType(String message,Integer clientType) throws IOException {
		Map<String, WebsocketClient> clientMap  = clientTypeMap.get(clientType);
		if(clientMap != null) {
			 Set<String> clientCodesSet = clientMap.keySet();
			 for (String clientCode : clientCodesSet) {
				 if(StringUtils.isNotEmpty(clientCode)) {
					 WebsocketClient item  = clientMap.get(clientCode);
					 if(null!=item && null != item.session) {
						 item.session.getAsyncRemote().sendText(message);
					 }
				 }
			 }
		 }
	 }

	/**
	 * 针对所有渠道所有终端用户发送消息
	 * @param message String 消息内容
	 * @throws IOException
	 */
	 public static void sendMessageAll(String message) throws IOException {
		 for(Map<String, WebsocketClient> clientMap:clientTypeMap.values()){
			 if(clientMap != null) {
				 for (WebsocketClient item : clientMap.values()) {
					 if(item != null && null != item.session) {
						 item.session.getAsyncRemote().sendText(message);
					 }
				 }
			 }
		 }

	 } 
	 public static synchronized int getOnlineCount() { 
	     return WebsocketClient.onlineCount;
	 } 
	 public static synchronized void addOnlineCount() { 
		 WebsocketClient.onlineCount++; 
	 } 
	 public static synchronized void subOnlineCount() { 
		 WebsocketClient.onlineCount--;
	 }

	 public static synchronized void syncOnlineCount() {
		 int count = 0;
		 for(Map<String, WebsocketClient> clientMap:clientTypeMap.values()){
			 if(clientMap != null) {
				 count = count + clientMap.size();
			 }
		 }
		 WebsocketClient.onlineCount = count;
	 }
}
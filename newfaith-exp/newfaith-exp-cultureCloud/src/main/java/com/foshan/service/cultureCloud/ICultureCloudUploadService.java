package com.foshan.service.cultureCloud;

import javax.servlet.http.HttpServletRequest;

import org.springframework.web.multipart.MultipartFile;

import com.foshan.form.request.UploadReq;
import com.foshan.form.response.IResponse;

import java.io.IOException;

public interface ICultureCloudUploadService {

    /**
     * 上传文化云文件
     */
    public IResponse uploadCultureCloudLiveReplay(HttpServletRequest request, MultipartFile[] file, UploadReq req) throws IOException;
}
package com.foshan.service.cultureCloud.impl;

import com.foshan.dao.cultureCloud.ICultureCloudLiveDao;
import com.foshan.dao.cultureCloud.ICultureCloudTagDao;
import com.foshan.dao.generic.Page;
import com.foshan.entity.cultureCloud.CultureCloudLiveEntity;
import com.foshan.entity.cultureCloud.CultureCloudMemberEntity;
import com.foshan.entity.cultureCloud.CultureCloudTagEntity;
import com.foshan.entity.cultureCloud.CultureCloudUserEntity;
import com.foshan.form.cultureCloud.request.CultureCloudLiveReq;
import com.foshan.form.cultureCloud.response.cultureCloudLive.GetCultureCloudLiveInfoRes;
import com.foshan.form.cultureCloud.response.cultureCloudLive.GetCultureCloudLiveListRes;
import com.foshan.form.cultureCloud.CultureCloudTypeForm;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.annotation.Audit;
import com.foshan.service.cultureCloud.ICultureCloudLiveService;
import com.foshan.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import com.foshan.form.cultureCloud.CultureCloudLiveForm;
import java.util.ArrayList;
import java.util.List;

@Service("cultureCloudLiveService")
@Transactional
public class CultureCloudLiveServiceImpl extends GenericCultureCloudService implements ICultureCloudLiveService {
    
    @Autowired
    private ICultureCloudLiveDao cultureCloudLiveDao;
    
    @Autowired
    private ICultureCloudTagDao cultureCloudTagDao;
    
    @Override
    @Audit(operate = "新增直播")
    public IResponse addLive(CultureCloudLiveReq req) {
        GenericResponse res = new GenericResponse();
        
        // 获取当前登录用户
        Object userObj = getPrincipal(true);
        if (userObj == null) {
            res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO);
            return res;
        }

        // 判断用户类型
        boolean isAdmin = userObj instanceof CultureCloudUserEntity;

        if (!isAdmin) {
            res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO);
            return res;
        }
        
        if (validateLiveRequest(req, res)) {
            return res;
        }
        
        try {
            CultureCloudLiveEntity live = new CultureCloudLiveEntity();
            BeanUtils.copyProperties(req, live);
            
            live.setLiveUUID(createUUId());
            live.setLiveStartTime(new Timestamp(DateUtil.parseLongFormat(req.getLiveStartTimeStr()).getTime()));
            live.setLiveEndTime(new Timestamp(DateUtil.parseLongFormat(req.getLiveEndTimeStr()).getTime()));
            
            // 处理艺术类型标签
            if (StringUtils.isNotEmpty(req.getArtTypeIdList())) {
                String[] artTypeIds = req.getArtTypeIdList().split(",");
                for (String artTypeId : artTypeIds) {
                    CultureCloudTagEntity tag = cultureCloudTagDao.get(Integer.parseInt(artTypeId.trim()));
                    if (tag != null) {
                        live.getTagList().add(tag);
                    }
                }
            }
            
            setDefaultValues(live);
            cultureCloudLiveDao.save(live);
            
            res.setRet(ResponseContext.RES_SUCCESS_CODE);
            res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            
        } catch (Exception e) {
            res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
            res.setRetInfo("保存失败：" + e.getMessage());
        }
        
        return res;
    }
    
    @Override
    @Audit(operate = "修改直播")
    public IResponse modifyLive(CultureCloudLiveReq req) {
        GenericResponse res = new GenericResponse();

        // 获取当前登录用户
        Object userObj = getPrincipal(true);
        if (userObj == null) {
            res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO);
            return res;
        }

        // 判断用户类型
        boolean isAdmin = userObj instanceof CultureCloudUserEntity;

        if (!isAdmin) {
            res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO);
            return res;
        }

        if (req.getId() == null) {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo("直播ID不能为空");
            return res;
        }
        
        CultureCloudLiveEntity live = cultureCloudLiveDao.get(req.getId());
        if (live == null) {
            res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
            res.setRetInfo("直播不存在");
            return res;
        }
        
        if (validateLiveRequest(req, res)) {
            return res;
        }
        
        try {
            BeanUtils.copyProperties(req, live, "id", "liveUUID");
            live.setLiveStatus(0);
            live.setIsDelete(1);
            live.setLiveStartTime(new Timestamp(DateUtil.parseLongFormat(req.getLiveStartTimeStr()).getTime()));
            live.setLiveEndTime(new Timestamp(DateUtil.parseLongFormat(req.getLiveEndTimeStr()).getTime()));
            
            // 处理艺术类型标签
            live.getTagList().clear(); // 清除原有标签
            if (StringUtils.isNotEmpty(req.getArtTypeIdList())) {
                String[] artTypeIds = req.getArtTypeIdList().split(",");
                for (String artTypeId : artTypeIds) {
                    CultureCloudTagEntity tag = cultureCloudTagDao.get(Integer.parseInt(artTypeId.trim()));
                    if (tag != null) {
                        live.getTagList().add(tag);
                    }
                }
            }
            
            cultureCloudLiveDao.update(live);
            
            res.setRet(ResponseContext.RES_SUCCESS_CODE);
            res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            
        } catch (Exception e) {
            res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
            res.setRetInfo("修改失败：" + e.getMessage());
        }
        
        return res;
    }
    
    @Override
    public IResponse getLiveList(CultureCloudLiveReq req) {
        GetCultureCloudLiveListRes res = new GetCultureCloudLiveListRes();
        Page<CultureCloudLiveEntity> page = new Page<CultureCloudLiveEntity>();
        page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
        page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
        page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);

        // 获取当前登录用户
        Object userObj = getPrincipal(true);

        // 判断用户类型
        boolean isAdmin = userObj instanceof CultureCloudUserEntity;

        // 根据是否有艺术类型筛选来决定是否需要JOIN标签表
        StringBuilder hql = new StringBuilder("select distinct a from CultureCloudLiveEntity a");
        if (StringUtils.isNotEmpty(req.getArtTypeIdList())) {
            hql.append(" join a.tagList t");
        }
        hql.append(" where liveStatus in (");
        hql.append(isAdmin ? (null != req.getLiveStatus() ? req.getLiveStatus(): "0,1,2,3,4,5,6,7)") :"6)");
        hql.append(StringUtils.isNotEmpty(req.getLiveTitle()) ? " and a.liveTitle like '%" + req.getLiveTitle() + "%'" : "");
        hql.append(null != req.getLiveStatus() ? " and a.liveStatus=" + req.getLiveStatus() : "");
        hql.append(null != req.getIsDelete() ? " and a.isDelete=" + req.getIsDelete() : " and a.isDelete=1");
        hql.append(null != req.getPlatformType() ? " and a.platformType=" + req.getPlatformType() : "");

        // 直播分类筛选
        if (null != req.getLiveCut()) {
            hql.append(" and a.liveCut=" + req.getLiveCut());
        }

        // 艺术类型筛选
        if (StringUtils.isNotEmpty(req.getArtTypeIdList())) {
            hql.append(" and t.id in (" + req.getArtTypeIdList() + ")");
        }

        // 根据liveActivityTimeStatus筛选
        if (null != req.getLiveActivityTimeStatus()) {
            if (req.getLiveActivityTimeStatus() == 1) {
                // 正在直播：当前时间在开始时间和结束时间之间
                hql.append(" and current_timestamp() >= a.liveStartTime and current_timestamp() <= a.liveEndTime");
            } else if (req.getLiveActivityTimeStatus() == 2) {
                // 尚未开始：当前时间小于开始时间
                hql.append(" and current_timestamp() < a.liveStartTime");
            } else if (req.getLiveActivityTimeStatus() == 3) {
                // 已结束：当前时间大于结束时间
                hql.append(" and current_timestamp() > a.liveEndTime");
            }
        }

        hql.append(" ORDER BY a.id desc");

        page = cultureCloudLiveDao.queryPage(page, hql.toString());

        // 转换Entity为Form
        List<CultureCloudLiveForm> formList = new ArrayList<>();
        if (page.getResultList() != null) {
            for (CultureCloudLiveEntity entity : page.getResultList()) {
                CultureCloudLiveForm form = new CultureCloudLiveForm();
                BeanUtils.copyProperties(entity, form);
                form.setLiveId(entity.getId());
                form.setLiveActivityTimeStatus(calculateLiveActivityTimeStatus(entity));
                form.setArtTypeList(buildArtTypeList(entity));
                formList.add(form);
            }
        }

        Page<CultureCloudLiveForm> formPage = new Page<>();
        formPage.setResultList(formList);
        formPage.setTotalCount(page.getTotalCount());
        formPage.setPageSize(page.getPageSize());
        formPage.setCurrentPage(page.getCurrentPage());

        res.setPage(formPage);
        res.setRet(ResponseContext.RES_SUCCESS_CODE);
        res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

        return res;
    }
    
    @Override
    public IResponse getLiveInfo(CultureCloudLiveReq req) {
        GetCultureCloudLiveInfoRes res = new GetCultureCloudLiveInfoRes();

        // 获取当前登录用户
        Object userObj = getPrincipal(true);

        // 判断用户类型
        boolean isMember = userObj instanceof CultureCloudMemberEntity;

        if (req.getId() == null) {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo("直播ID不能为空");
            return res;
        }
        
        try {
            CultureCloudLiveEntity live = cultureCloudLiveDao.get(req.getId());
            if (live == null) {
                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
                res.setRetInfo("直播不存在");
                return res;
            }

            if (null == userObj || isMember){
                if (live.getLiveStatus() != 6) {
                    res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
                    res.setRetInfo("直播未发布，无法查看");
                    return res;
                }
            }

            CultureCloudLiveForm form = new CultureCloudLiveForm();
            BeanUtils.copyProperties(live, form);
            form.setLiveId(live.getId());
            form.setLiveActivityTimeStatus(calculateLiveActivityTimeStatus(live));
            form.setArtTypeList(buildArtTypeList(live));
            res.setLive(form);
            res.setRet(ResponseContext.RES_SUCCESS_CODE);
            res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            
        } catch (Exception e) {
            res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
            res.setRetInfo("查询失败：" + e.getMessage());
        }
        
        return res;
    }
    
    @Override
    @Audit(operate = "删除直播")
    public IResponse deleteLive(CultureCloudLiveReq req) {
        GenericResponse res = new GenericResponse();

        // 获取当前登录用户
        Object userObj = getPrincipal(true);
        if (userObj == null) {
            res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO);
            return res;
        }

        // 判断用户类型
        boolean isAdmin = userObj instanceof CultureCloudUserEntity;

        if (!isAdmin) {
            res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO);
            return res;
        }

        if (req.getId() == null) {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo("直播ID不能为空");
            return res;
        }
        
        try {
            CultureCloudLiveEntity live = cultureCloudLiveDao.get(req.getId());
            if (live == null) {
                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
                res.setRetInfo("直播不存在");
                return res;
            }
            
            cultureCloudLiveDao.delete(live);
            
            res.setRet(ResponseContext.RES_SUCCESS_CODE);
            res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            
        } catch (Exception e) {
            res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
            res.setRetInfo("删除失败：" + e.getMessage());
        }
        
        return res;
    }
    
    @Override
    @Audit(operate = "发布直播")
    public IResponse publishLive(CultureCloudLiveReq req) {
        GenericResponse res = new GenericResponse();

        // 获取当前登录用户
        Object userObj = getPrincipal(true);
        if (userObj == null) {
            res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO);
            return res;
        }

        // 判断用户类型
        boolean isAdmin = userObj instanceof CultureCloudUserEntity;

        if (!isAdmin) {
            res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO);
            return res;
        }

        if (req.getId() == null) {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo("直播ID不能为空");
            return res;
        }

        if (req.getLiveStatus() == null) {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo("发布状态不能为空");
            return res;
        }
        
        try {
            CultureCloudLiveEntity live = cultureCloudLiveDao.get(req.getId());
            if (live == null) {
                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
                res.setRetInfo("直播不存在");
                return res;
            }

            // 根据操作类型进行不同的处理
            if (req.getLiveStatus() == 6) {
                // 上架操作：检查审核状态，只有审核通过的直播才能上架
                if (live.getLiveStatus() != 2) {
                    res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
                    res.setRetInfo("只有审核通过状态的直播才能上架");
                    return res;
                }

                live.setLiveStatus(6); // 上架状态
                res.setRetInfo("直播上架成功");
                
            } else if (req.getLiveStatus() == 2) {
                // 下架操作：只有已发布的直播才能下架
                if (live.getLiveStatus() != 6) {
                    res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
                    res.setRetInfo("只有已发布状态的直播才能下架");
                    return res;
                }
                
                live.setLiveStatus(2); // 下架到已审核状态
                res.setRetInfo("直播下架成功");
                
            } else {
                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
                res.setRetInfo("发布状态参数错误，只能设置为上架或下架");
                return res;
            }
            
            cultureCloudLiveDao.update(live);
            res.setRet(ResponseContext.RES_SUCCESS_CODE);
            
        } catch (Exception e) {
            res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
            res.setRetInfo("操作失败：" + e.getMessage());
        }
        
        return res;
    }
    
    @Override
    @Audit(operate = "审核直播")
    public IResponse auditLive(CultureCloudLiveReq req) {
        GenericResponse res = new GenericResponse();

        // 获取当前登录用户
        Object userObj = getPrincipal(true);
        if (userObj == null) {
            res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO);
            return res;
        }

        // 判断用户类型，只有管理员可以审核
        boolean isAdmin = userObj instanceof CultureCloudUserEntity;
        if (!isAdmin) {
            res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
            res.setRetInfo("只有管理员可以审核直播");
            return res;
        }

        if (req.getId() == null) {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo("直播ID不能为空");
            return res;
        }

        if (req.getLiveStatus() == null) {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo("审核状态不能为空");
            return res;
        }

        // 验证审核状态是否合法 (2-已审核通过, 7-审核未通过)
        if (req.getLiveStatus() != 2 && req.getLiveStatus() != 7) {
            res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
            res.setRetInfo("审核状态参数错误，只能设置为通过(2)或未通过(7)");
            return res;
        }
        
        try {
            CultureCloudLiveEntity live = cultureCloudLiveDao.get(req.getId());
            if (live == null) {
                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
                res.setRetInfo("直播不存在");
                return res;
            }

            // 检查当前状态是否可以审核
            if (live.getLiveStatus() != 0 && live.getLiveStatus() != 3) {
                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
                res.setRetInfo("只有未审核(0)或待审核(3)状态的直播才能进行审核");
                return res;
            }
            
            // 更新审核状态
            live.setLiveStatus(req.getLiveStatus());
            cultureCloudLiveDao.update(live);
            
            String auditResult = req.getLiveStatus() == 2 ? "审核通过" : "审核未通过";
            res.setRet(ResponseContext.RES_SUCCESS_CODE);
            res.setRetInfo("直播" + auditResult);
            
        } catch (Exception e) {
            res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
            res.setRetInfo("审核失败：" + e.getMessage());
        }
        
        return res;
    }
    
    @Override
    @Audit(operate = "修改直播视频地址")
    public IResponse updateLiveVideoUrl(CultureCloudLiveReq req) {
        GenericResponse res = new GenericResponse();

        // 获取当前登录用户
        Object userObj = getPrincipal(true);
        if (userObj == null) {
            res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO);
            return res;
        }

        if (req.getId() == null) {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo("直播ID不能为空");
            return res;
        }

        if (StringUtils.isEmpty(req.getLiveVideoUrl()) && StringUtils.isEmpty(req.getLiveUploadLink())) {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo("直播视频地址不能为空");
            return res;
        }
        
        try {
            CultureCloudLiveEntity live = cultureCloudLiveDao.get(req.getId());
            if (live == null) {
                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
                res.setRetInfo("直播不存在");
                return res;
            }

            // 权限检查：只有发布者自己或系统管理员才能修改
            boolean isAdmin = userObj instanceof CultureCloudUserEntity;
            boolean isCreator = false;
            
            if (isAdmin) {
                // 管理员用户，检查是否是创建者
                CultureCloudUserEntity adminUser = (CultureCloudUserEntity) userObj;
                isCreator = adminUser.getId().toString().equals(live.getLiveCreateUser());
            } else {
                // 非管理员用户，无权限修改
                res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
                res.setRetInfo("只有发布者自己或系统管理员才能修改直播视频地址");
                return res;
            }

            // 如果不是管理员且不是创建者，则无权限
            if (!isAdmin && !isCreator) {
                res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
                res.setRetInfo("只有发布者自己或系统管理员才能修改直播视频地址");
                return res;
            }
            
            // 更新直播视频地址
            live.setLiveVideoUrl(StringUtils.isNotEmpty(req.getLiveVideoUrl())?req.getLiveVideoUrl():live.getLiveVideoUrl());
            live.setLiveUploadLink(StringUtils.isNotEmpty(req.getLiveUploadLink())?req.getLiveUploadLink():live.getLiveUploadLink());
            cultureCloudLiveDao.update(live);
            
            res.setRet(ResponseContext.RES_SUCCESS_CODE);
            res.setRetInfo("直播视频地址修改成功");
            
        } catch (Exception e) {
            res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
            res.setRetInfo("修改失败：" + e.getMessage());
        }
        
        return res;
    }
    
    private boolean validateLiveRequest(CultureCloudLiveReq req, GenericResponse res) {
        if (StringUtils.isEmpty(req.getLiveTitle())) {
            res.setRet(ResponseContext.RES_NULL_ERROR_INFO);
            res.setRetInfo("直播名称不能为空");
            return true;
        }
        
        if (StringUtils.isEmpty(req.getLiveCreateUser())) {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo("直播用户不能为空");
            return true;
        }
        
        if (req.getPlatformType() == null) {
            res.setRet(ResponseContext.RES_NULL_ERROR_INFO);
            res.setRetInfo("平台类型不能为空");
            return true;
        }
        
        if (req.getPlatformType() == 2) {
            if (StringUtils.isEmpty(req.getPcUrl())) {
                res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
                res.setRetInfo("第三方平台PC链接不能为空");
                return true;
            }
            if (StringUtils.isEmpty(req.getH5Url())) {
                res.setRet(ResponseContext.RES_NULL_ERROR_INFO);
                res.setRetInfo("第三方平台H5链接不能为空");
                return true;
            }
        }
        
        if (StringUtils.isEmpty(req.getLiveCoverImg())) {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo("封面图片不能为空");
            return true;
        }
        
        if (StringUtils.isEmpty(req.getLiveAddress())) {
            res.setRet(ResponseContext.RES_DATA_NULL_CODE);
            res.setRetInfo("活动地点不能为空");
            return true;
        }
        
        if (StringUtils.isEmpty(req.getLiveStartTimeStr()) || StringUtils.isEmpty(req.getLiveEndTimeStr())) {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo("直播时间不能为空");
            return true;
        }
        
        return false;
    }
    
    private void setDefaultValues(CultureCloudLiveEntity live) {
        if (live.getLiveStatus() == null) {
            live.setLiveStatus(0);
        }
        if (live.getIsDelete() == null) {
            live.setIsDelete(1);
        }
        if (live.getLiveType() == null) {
            live.setLiveType(2);
        }
        if (live.getMustLogin() == null) {
            live.setMustLogin(1);
        }
        if (live.getUseAutoRecord() == null) {
            live.setUseAutoRecord(1);
        }
        if (live.getLiveIsTranscoding() == null) {
            live.setLiveIsTranscoding(0);
        }
        if (live.getLiveIsDelay() == null) {
            live.setLiveIsDelay(0);
        }
    }

    private Integer calculateLiveActivityTimeStatus(CultureCloudLiveEntity entity) {
        if (entity.getLiveStartTime() == null || entity.getLiveEndTime() == null) {
            return null;
        }
        
        Timestamp now = new Timestamp(System.currentTimeMillis());
        
        if (now.before(entity.getLiveStartTime())) {
            return 2; // 尚未开始
        } else if (now.after(entity.getLiveEndTime())) {
            return 3; // 已结束
        } else {
            return 1; // 正在直播
        }
    }

    private List<CultureCloudTypeForm> buildArtTypeList(CultureCloudLiveEntity entity) {
        List<CultureCloudTypeForm> artTypeList = new ArrayList<>();
        if (entity.getTagList() != null && !entity.getTagList().isEmpty()) {
            entity.getTagList().forEach(tag -> {
                if (tag.getCategory() != null && tag.getCategory() == 3) { // 3表示艺术类型
                    CultureCloudTypeForm typeForm = new CultureCloudTypeForm();
                    typeForm.setCultureCloudTypeId(tag.getId());
                    typeForm.setCategory(tag.getCategory());
                    typeForm.setTypeName(tag.getTagName());
                    artTypeList.add(typeForm);
                }
            });
        }
        return artTypeList;
    }
}

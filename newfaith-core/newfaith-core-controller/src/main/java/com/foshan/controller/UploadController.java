package com.foshan.controller;

import java.math.BigDecimal;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.UploadedFile;
import com.foshan.form.request.FileShardReq;
import com.foshan.form.request.UploadReq;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.upload.UploadRes;

import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "融合平台--上传模块")
@RestController
public class UploadController extends BaseController{


	@ApiOperation(value = "上传图片(uploadImage)", httpMethod = "POST", notes = "上传图片")
	@ResponseBody
	@RequestMapping(value = "/uploadImage",method = { RequestMethod.POST},produces = MediaType.APPLICATION_JSON_VALUE)
	public UploadRes uploadImage(HttpServletRequest request, HttpServletResponse response,
			@ModelAttribute UploadedFile uploadedFile, Integer parentAssetId, 
			Integer assetSpecId, Integer serviceId,Integer imageWidth,Integer imageHeight,Integer smallImageWidth
			,Integer smallImageHeight,Integer createSmallImage,Integer createMiddleImage,Integer onShelves,
			String tag,Integer variableFormat,Integer filesize,String accuracy ,Integer middleImageHeight,Integer middleImageWidth
			,String summaryShort,Integer isOriginalSize) throws Exception {
		//long start = System.currentTimeMillis();
		request.setCharacterEncoding("utf-8"); 
		MultipartFile[] multipartFile = uploadedFile.getMultipartFile();
		UploadReq req = new UploadReq();
		req.setAccuracy(StringUtils.isNotEmpty(accuracy) ? new BigDecimal(accuracy) : null);
		req.setAssetSpecId(null!=assetSpecId ? assetSpecId : null );
		req.setFilesize(null!=filesize ? filesize : null);
		req.setImageHeight(null!=imageHeight ? imageHeight : null);
		req.setImageWidth(null!=imageWidth ? imageWidth : null);
		req.setMiddleImageHeight(null!=middleImageHeight ? middleImageWidth : null);
		req.setMiddleImageWidth(null!=middleImageWidth ? middleImageWidth : null);
		req.setOnShelves(null!=onShelves ? (1==onShelves ? true :false) : false);
		req.setParentAssetId(null!=parentAssetId ? parentAssetId : null);
		req.setServiceId(null!=serviceId ? serviceId : null);
		req.setSmallImageHeight(null!=smallImageHeight ? smallImageHeight : null);
		req.setSmallImageWidth(null!=smallImageWidth ? smallImageWidth : null);
		req.setTag(StringUtils.isNotEmpty(tag) ? tag : "" );
		req.setVariableFormat(null!=variableFormat ? variableFormat: null);
		req.setSummaryShort(summaryShort);
		req.setIsOriginalSize(isOriginalSize);

		UploadRes res =(UploadRes)uploadService.uploadImage(multipartFile,request,req);
		return res;
	}
	
	
	
//	@ApiOperation(value = "获取栏目上架列表(deleteImage)", httpMethod = "POST", notes = "获取栏目上架列表")
//	@ResponseBody
//	@RequestMapping(value = "/upload", method = {
//			RequestMethod.POST, RequestMethod.GET  }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
//	public UploadRes deleteImage(String jsonData, HttpServletRequest request, HttpServletResponse response)
//			throws JsonParseException, JsonMappingException, IOException {
//		if (StringUtils.isEmpty(jsonData)) {
//			BufferedReader reader = new BufferedReader(new InputStreamReader(request.getInputStream()));
//			jsonData = IOUtils.read(reader);
//		}
//		jsonData = URLDecoder.decode(jsonData, "UTF-8");
//		UploadReq req = (UploadReq) mapper.readValue(jsonData, UploadReq.class);
//		UploadRes res =(UploadRes) uploadService.deleteImage(req);
//		return res;
//	}
	
	
	// 删除图片
	@ApiOperation(value = "删除图片(deleteImage)", httpMethod = "POST", notes = "删除图片，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/deleteImage", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public UploadRes deleteImage(@RequestBody UploadReq req, HttpServletRequest request)
			throws JsonProcessingException {
		UploadRes res = (UploadRes) uploadService.deleteImage(req);
		return res;
	}

	@ApiOperation(value = "上传文件(uploadFile)", httpMethod = "POST", notes = "上传文件")
	@ResponseBody
	@RequestMapping(value = "/uploadFile",method = { RequestMethod.POST},produces = MediaType.APPLICATION_JSON_VALUE)
	public UploadRes uploadFile(HttpServletRequest request, HttpServletResponse response,
			@ModelAttribute UploadedFile uploadedFile, Integer parentAssetId, 
			Integer assetSpecId, Integer serviceId,Integer imageWidth,Integer imageHeight,Integer smallImageWidth
			,Integer smallImageHeight,Integer createSmallImage,Integer createMiddleImage,Integer onShelves,
			String tag,Integer variableFormat,Integer filesize,String accuracy ,Integer middleImageHeight,Integer middleImageWidth ,
								String summaryShort,String fileName) throws Exception {
		//long start = System.currentTimeMillis();
		request.setCharacterEncoding("utf-8"); 
		MultipartFile[] multipartFile = uploadedFile.getMultipartFile();
		UploadReq req = new UploadReq();
		req.setAccuracy(StringUtils.isNotEmpty(accuracy) ? new BigDecimal(accuracy) : null);
		req.setAssetSpecId(null!=assetSpecId ? assetSpecId : null );
		req.setFilesize(null!=filesize ? filesize : null);
		req.setImageHeight(null!=smallImageHeight ? smallImageHeight : null);
		req.setImageWidth(null!=smallImageWidth ? smallImageWidth : null);
		req.setMiddleImageHeight(null!=middleImageHeight ? middleImageWidth : null);
		req.setMiddleImageWidth(null!=middleImageWidth ? middleImageWidth : null);
		req.setOnShelves(null!=onShelves ? (1==onShelves ? true :false) : false);
		req.setParentAssetId(null!=parentAssetId ? parentAssetId : null);
		req.setServiceId(null!=serviceId ? serviceId : null);
		req.setSmallImageHeight(null!=smallImageHeight ? smallImageHeight : null);
		req.setSmallImageWidth(null!=smallImageWidth ? smallImageWidth : null);
		req.setTag(StringUtils.isNotEmpty(tag) ? tag : "" );
		req.setVariableFormat(null!=variableFormat ? variableFormat: null);
		req.setSummaryShort(summaryShort);
		UploadRes res =(UploadRes)uploadService.uploadFile(request,multipartFile,req);
		return res;
	}
	
	@ApiOperation(value = "文件分片上传(uploadShard)", httpMethod = "POST", notes = "文件分片上传<p>1:shardIndex、shardSize、shard、shardTotal、fileName、fileSuffix、key不能为空；")
	@ResponseBody
	@RequestMapping(value = "/uploadShard", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public UploadRes uploadShard(@RequestBody FileShardReq req, HttpServletRequest request) throws Exception {
		UploadRes res = (UploadRes) uploadService.uploadShard(req);
		return res;
	}

	
	// 图片上传组件需要的接口
	@ApiOperation(value = "图片上传组件需要的接口(getEmpty)", httpMethod = "POST", notes = "图片上传组件需要的接口，传空{}即可,无逻辑；")
	@ResponseBody
	@RequestMapping(value = "/getEmpty", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse getEmpty(@RequestBody UploadReq req, HttpServletRequest request)
			throws JsonProcessingException {
		return new GenericResponse();
	}
}

package com.foshan.form.community.response.communityUser;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取摄像头播放地址返回对象(CommunityUserRes)")
@JsonInclude(Include.NON_NULL)
public class GetCameraPlayUrlRes extends BaseResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = 7490476786126811232L;
	/**
	 * 获取摄像头播放地址返回对象
	 */

	@ApiModelProperty(value = "过期时间")
	private String expireTime;
	@ApiModelProperty(value = "播放地址")
	private String playUrl;
}

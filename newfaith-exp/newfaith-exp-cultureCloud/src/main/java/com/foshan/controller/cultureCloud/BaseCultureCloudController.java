package com.foshan.controller.cultureCloud;

import javax.annotation.Resource;

import com.foshan.controller.BaseController;
import com.foshan.service.cultureCloud.*;



public class BaseCultureCloudController extends BaseController {

    @Resource(name = "cultureCloudAssetService")
	protected ICultureCloudAssetService cultureCloudAssetService;
	@Resource(name = "cultureCloudTagService")
    protected ICultureCloudTagService cultureCloudTagService;
    @Resource(name = "cultureCloudTypeService")
    protected ICultureCloudTypeService cultureCloudTypeService;
    @Resource(name = "cultureCloudMemberService")
    protected ICultureCloudMemberService cultureCloudMemberService;
    @Resource(name = "cultureCloudDepartmentService")
    protected ICultureCloudDepartmentService cultureCloudDepartmentService;
    @Resource(name = "cultureCloudUserService")
    protected ICultureCloudUserService cultureCloudUserService;
    @Resource(name = "cultureCloudAddressService")
    protected ICultureCloudAddressService cultureCloudAddressService;
    @Resource(name = "cultureCloudVenueService")
    protected ICultureCloudVenueService cultureCloudVenueService;

    @Resource(name = "cultureCloudVenueTicketService")
    protected ICultureCloudVenueTicketService cultureCloudVenueTicketService;

    @Resource(name = "cultureCloudVenueTicketOrderService")
    protected ICultureCloudVenueTicketOrderService cultureCloudVenueTicketOrderService;
    @Resource(name = "cultureCloudActivityService")
    protected ICultureCloudActivityService cultureCloudActivityService;

    @Resource(name = "cultureCloudActivityEventService")
    protected ICultureCloudActivityEventService cultureCloudActivityEventService;
    @Resource(name = "cultureCloudRoomService")
    protected ICultureCloudRoomService cultureCloudRoomService;
    @Resource(name = "cultureCloudActivityOrderService")
    protected ICultureCloudActivityOrderService cultureCloudActivityOrderService;

    @Resource(name = "cultureCloudRoomBookService")
    protected ICultureCloudRoomBookService cultureCloudRoomBookService;
    
    @Resource(name = "cultureCloudUpshelfColumnService")
    protected ICultureCloudUpshelfColumnService cultureCloudUpshelfColumnService;
    
    @Resource(name = "cultureCloudVenueSeatTemplateService")
    protected ICultureCloudVenueSeatTemplateService cultureCloudVenueSeatTemplateService;
    
    @Resource(name = "cultureCloudSeatAccountService")
    protected ICultureCloudSeatAccountService cultureCloudSeatAccountService;
    
    @Resource(name = "cultureCloudVenueSeatService")
    protected ICultureCloudVenueSeatService cultureCloudVenueSeatService;
    
    @Resource(name = "cultureCloudSensitiveWordsService")
    protected ICultureCloudSensitiveWordsService cultureCloudSensitiveWordsService;

    @Resource(name = "cultureCloudRoomOrderService")
    protected ICultureCloudRoomOrderService cultureCloudRoomOrderService;
    
    @Resource(name = "activityStatisticsService")
    protected IActivityStatisticsService activityStatisticsService;
    
    @Resource(name = "cultureCloudStatisticsFlowService")
    protected ICultureCloudStatisticsFlowService cultureCloudStatisticsFlowService;

    @Resource(name = "cultureCloudWhgdSyncService")
    protected ICultureCloudWhgdSyncService cultureCloudWhgdSyncService;

    @Resource(name = "cultureCloudOfflineTrainingOrderService")
    protected ICultureCloudOfflineTrainingOrderService cultureCloudOfflineTrainingOrderService;

    @Resource(name = "cultureCloudOfflineTrainingService")
    protected ICultureCloudOfflineTrainingService cultureCloudOfflineTrainingService;

    @Resource(name = "cultureCloudLiveService")
    protected ICultureCloudLiveService cultureCloudLiveService;
    
    @Resource(name = "cultureCloudUploadService")
    protected ICultureCloudUploadService cultureCloudUploadService;
}

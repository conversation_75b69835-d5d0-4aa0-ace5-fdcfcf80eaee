package com.foshan.service.impl;

import static java.util.Comparator.comparingInt;
import static java.util.stream.Collectors.groupingBy;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import javax.transaction.Transactional;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.foshan.dao.generic.Page;
import com.foshan.entity.MenuEntity;
import com.foshan.entity.PermissionEntity;
import com.foshan.entity.RoleEntity;
import com.foshan.entity.UserEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.MenuForm;
import com.foshan.form.MetaForm;
import com.foshan.form.PermissionForm;
import com.foshan.form.RoleForm;
import com.foshan.form.RouterForm;
import com.foshan.form.request.MenuReq;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.form.response.menu.AddMenuRes;
import com.foshan.form.response.menu.GetMenuListRes;
import com.foshan.form.response.menu.GetMenuRes;
import com.foshan.form.response.menu.ModifyMenuRes;
import com.foshan.form.response.permission.GetRouterTreeRes;
import com.foshan.service.IMenuService;
import com.foshan.service.annotation.Audit;

import io.swagger.annotations.ApiModelProperty;

@Transactional
@Service("menuService")
public class MenuServiceImpl extends GenericService implements IMenuService {

	@Override
	@Audit(operate = "新增菜单")
	public AddMenuRes addMenu(MenuReq req) {
		AddMenuRes res = new AddMenuRes();
		UserEntity loginUser = getCurrentUser();
		if(null == loginUser) {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
			return res;
		}
		MenuEntity menu = new MenuEntity();
		menu.setMenuName(req.getMenuName());
		menu.setMenuType(req.getMenuType());
		menu.setHref(req.getHref());
		menu.setDescription(req.getDescription());
		
		menu.setDescription(req.getDescription());
		menu.setPath(req.getPath());
		menu.setComponent(req.getComponent());
		menu.setVisible(req.getVisible());
		menu.setIsCache(req.getIsCache());
		menu.setIsFrame(req.getIsFrame());
		menu.setOrderNum(req.getOrderNum());
		menu.setStatus(req.getStatus());
		menu.setIcon(req.getIcon());
		menu.setMenuCode(req.getMenuCode());
		menu.setClientSide(null!=req.getClientSide() ? req.getClientSide() : 0);
		for (RoleEntity role : loginUser.getRoleList()) {
			if (!role.getMenuList().contains(menu)) {
				role.getMenuList().add(menu);
				pupMenuInParentRole(role,menu);
			}
		}
		if (StringUtils.isNotEmpty(req.getRoleIdList())) {
			String[] roleIds = req.getRoleIdList().split(",");
			List<RoleEntity> roleList = new ArrayList<RoleEntity>();
			for (String roleId : roleIds) {
				RoleEntity role = roleDao.get(Integer.parseInt(roleId));
				if (null!=role && !role.getMenuList().contains(menu)) {
					role.getMenuList().add(menu);
				}
			}
			menu.setRoleList(roleList);
		}
		List<RoleEntity> roleList = roleDao.getListByHql("select distinct a from RoleEntity "
				+ "a where a.isRoot = 1 and a.isBuiltIn = 1 and a.roleState =" + EntityContext.RECORD_STATE_VALID,"");
		if (null != roleList) {
			for (RoleEntity role : roleList) {
				if (!role.getMenuList().contains(menu)) {
					role.getMenuList().add(menu);
				}
			}
		}
		
		if(null != req.getParentId()) {
			MenuEntity parentMenu = menuDao.get(req.getParentId());
			if(null != parentMenu) {
				menu.setParentMenu(parentMenu);
				menu.setClientSide(null!=req.getClientSide() ? req.getClientSide() : parentMenu.getClientSide());
			}

		}else {
			menu.setClientSide(null!=req.getClientSide() ? req.getClientSide() : 0);
		}
		if (StringUtils.isNotEmpty(req.getPermissionIdList())) {
			String[] permissionIds = req.getPermissionIdList().split(",");
			List<PermissionEntity> permissionList = new ArrayList<PermissionEntity>();
			for (String permissionId : permissionIds) {
				permissionList.add(permissionDao.get(Integer.parseInt(permissionId)));
			}
			menu.setPermissionList(permissionList);
		}
		menuDao.save(menu);
		MenuForm form = new MenuForm();
		form.setMenuId(menu.getId());
		form.setMenuName(req.getMenuName());
		form.setMenuType(req.getMenuType());
		res.setMenuForm(form);
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	
	public void pupMenuInParentRole(RoleEntity role ,MenuEntity menu) {
		if(null != role.getParentRole()){
			if (!role.getParentRole().getMenuList().contains(menu)) {
				role.getParentRole().getMenuList().add(menu);
				pupMenuInParentRole(role.getParentRole() , menu);
			}
		}
	}
	

	@Override
	@Audit(operate = "删除菜单")
	public BaseResponse deleteMenu(MenuReq req) {
		GenericResponse res = new GenericResponse();
		if(null != req.getMenuId()) {
			MenuEntity menu = menuDao.get(req.getMenuId());
			if(null != menu) {
				if(menu.getSubMenus().size()>0) {
					res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
					res.setRetInfo("删除失败！此菜单下还子菜单；");
					return res;
				}
				menu.setParentMenu(null);
				menu.setPermissionList(null);
				menu.setRoleList(null);
				menuDao.delete(menu);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_INVALID_PARAM_CODE);
			}
		}else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
	
		return res;
	}

	@Override
	@Audit(operate = "修改菜单")
	public ModifyMenuRes modifyMenu(MenuReq req) {
		ModifyMenuRes res = new ModifyMenuRes();
		if(null != req.getMenuId()) {
			MenuEntity menu = menuDao.get(req.getMenuId());
			if(null != menu) {
				menu.setMenuName(req.getMenuName());
				menu.setMenuType(req.getMenuType());
				menu.setHref(req.getHref());
				menu.setDescription(req.getDescription());
				menu.setMenuCode(StringUtils.isNotEmpty(req.getMenuCode()) ? req.getMenuCode() : menu.getMenuCode());
				menu.setPath(req.getPath());
				menu.setComponent(req.getComponent());
				menu.setVisible(req.getVisible());
				menu.setIsCache(req.getIsCache());
				menu.setIsFrame(req.getIsFrame());
				menu.setOrderNum(req.getOrderNum());
				menu.setStatus(req.getStatus());
				menu.setIcon(req.getIcon());
				menu.setMenuCode(req.getMenuCode());
				menu.setClientSide(null!=req.getClientSide() ? req.getClientSide() : menu.getClientSide());
				if(null != req.getParentId()) {
					MenuEntity parentMenu = menuDao.get(req.getParentId());
					if(null != parentMenu) {
						menu.setParentMenu(parentMenu);
						menu.setClientSide(parentMenu.getClientSide());
					}
				}
				if (StringUtils.isNotEmpty(req.getRoleIdList())) {
					menu.setRoleList(null);
					String[] roleIds = req.getRoleIdList().split(",");
					List<RoleEntity> roleList = new ArrayList<RoleEntity>();
					for (String roleId : roleIds) {
						roleList.add(roleDao.get(Integer.parseInt(roleId)));
					}
					menu.setRoleList(roleList);
				}
				if (StringUtils.isNotEmpty(req.getPermissionIdList())) {
					menu.setPermissionList(null);
					String[] permissionIds = req.getPermissionIdList().split(",");
					List<PermissionEntity> permissionList = new ArrayList<PermissionEntity>();
					for (String permissionId : permissionIds) {
						permissionList.add(permissionDao.get(Integer.parseInt(permissionId)));
					}
					menu.setPermissionList(permissionList);
				}
				MenuForm form = new MenuForm();
				form.setMenuId(menu.getId());
				form.setMenuName(req.getMenuName());
				form.setMenuType(req.getMenuType());
				res.setMenuForm(form);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_INVALID_PARAM_CODE);
			}
		}else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
	
		return res;
	}

	@Override
	public GetMenuRes getMenuInfo(MenuReq req) {
		GetMenuRes res = new GetMenuRes();
		MenuEntity menu = menuDao.get(req.getMenuId());
		MenuForm form = new MenuForm();
		form.setMenuId(menu.getId());
		form.setMenuName(menu.getMenuName());
		form.setMenuType(menu.getMenuType());
		form.setHref(menu.getHref());
		form.setDescription(menu.getDescription());
		//form.setSubMenus(getMenuByDepth(menu.getSubMenus(), req.getDepth()));
		menu.getSubMenus().forEach(o->{
			form.getSubMenus().add(getMenuForm(o, null!=req.getDepth()? req.getDepth():20,new HashMap<>(),req));
		});
		form.setDescription(menu.getDescription());
		form.setPath(menu.getPath());
		form.setComponent(menu.getComponent());
		form.setVisible(menu.getVisible());
		form.setIsCache(menu.getIsCache());
		form.setIsFrame(menu.getIsFrame());
		form.setOrderNum(menu.getOrderNum());
		form.setStatus(menu.getStatus());
		form.setIcon(menu.getIcon());
		form.setMenuCode(menu.getMenuCode());
		form.setClientSide(menu.getClientSide());
		if(null != menu.getParentMenu()) {
			MenuForm parentMenu = new MenuForm();
			parentMenu.setMenuId(menu.getParentMenu().getId());
			parentMenu.setMenuName(menu.getParentMenu().getMenuName());
			parentMenu.setMenuType(menu.getParentMenu().getMenuType());
			form.setParentMenu(parentMenu);
		}
		List<PermissionForm> permissions = new ArrayList<PermissionForm>();
		menu.getPermissionList().forEach(o->{
			PermissionForm f = new PermissionForm();
			f.setId(o.getId());
			f.setPermissionName(o.getPermissionName());
			f.setDisplayName(o.getDisplayName());
			f.setDescription(StringUtils.isNotEmpty(o.getDescription()) ? o.getDescription() : "");
			//f.setHref(StringUtils.isNotEmpty(o.getHref()) ? o.getHref() : "");
			//f.setGroupName(o.getGroupName());
			permissions.add(f);
		});
		List<RoleForm> roleList = new ArrayList<RoleForm>();
		menu.getRoleList().forEach(o->{
			RoleForm r = new RoleForm();
			r.setRoleId(o.getId());
			r.setRoleName(o.getRoleName());
			r.setBuiltIn(o.isBuiltIn());
			r.setDisplayName(o.getDisplayName());
			roleList.add(r);
		});
		form.setPermissionList(permissions);
		form.setRoleList(roleList);
		
		res.setMenuForm(form);
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	
	//按深度获取子菜单
	private List<MenuForm> getMenuByDepth(List<MenuEntity> menuList,Integer depth,
			Map<Integer, List<RoleEntity>> roleMap,MenuReq req){
		List<MenuForm> formList = new ArrayList<MenuForm>();
		depth--;
		for(MenuEntity menu : menuList) {
			boolean state = false;
			List<RoleForm> roleList = new ArrayList<RoleForm>();
			MenuForm form = new MenuForm();
			form.setIsBindingPermission(null!=req.getPermissionId() ? 0 : null);
			form.setIsBindingRole(null!=req.getRoleId()? 0 : null);
			form.setClientSide(menu.getClientSide());
			for(RoleEntity o : menu.getRoleList()){
				if(roleMap.containsKey(o.getId())) {
					state = true;
				}
				if(null!=req.getRoleId() && req.getRoleId().toString().equals(o.getId().toString())) {
					form.setIsBindingRole(1);
				}
				RoleForm r = new RoleForm();
				r.setRoleId(o.getId());
				r.setRoleName(o.getRoleName());
				r.setBuiltIn(o.isBuiltIn());
				r.setDisplayName(o.getDisplayName());
				roleList.add(r);
			}
			if(state) {
				form.setMenuId(menu.getId());
				form.setMenuName(menu.getMenuName());
				form.setMenuType(menu.getMenuType());
				form.setHref(menu.getHref());
				form.setDescription(menu.getDescription());
				form.setPath(menu.getPath());
				form.setComponent(menu.getComponent());
				form.setVisible(menu.getVisible());
				form.setIsCache(menu.getIsCache());
				form.setIsFrame(menu.getIsFrame());
				form.setOrderNum(menu.getOrderNum());
				form.setStatus(menu.getStatus());
				form.setIcon(menu.getIcon());
				form.setMenuCode(menu.getMenuCode());
				List<MenuEntity> subMenu = menu.getSubMenus();
				List<PermissionForm> permissions = new ArrayList<PermissionForm>();
				menu.getPermissionList().forEach(o->{
					if(null!=req.getPermissionId() && req.getPermissionId().toString().equals(o.getId().toString())) {
						form.setIsBindingPermission(1);
					}
					PermissionForm f = new PermissionForm();
					f.setId(o.getId());
					f.setPermissionName(o.getPermissionName());
					f.setDisplayName(o.getDisplayName());
					f.setDescription(StringUtils.isNotEmpty(o.getDescription()) ? o.getDescription() : "");
					//f.setHref(StringUtils.isNotEmpty(o.getHref()) ? o.getHref() : "");
					//f.setGroupName(o.getGroupName());
					permissions.add(f);
				});

				form.setPermissionList(permissions);
				form.setRoleList(roleList);
				if(depth > 1 && subMenu.size()>0) {
					subMenu.sort(comparingInt(MenuEntity::getOrderNum));
					List<MenuForm> subForm = getMenuByDepth(subMenu,depth,roleMap,req);
					form.setSubMenus(subForm);
				}
				formList.add(form);
			}
		}
		
		return formList;
	}
	
	public IResponse getMenuTree(MenuReq req) {
		GetMenuListRes res = new GetMenuListRes();
		UserEntity loginUser = getCurrentUser();
		if(null == loginUser) {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
			return res;
		}
		Map<Integer, List<RoleEntity>> collect = 
				(Map<Integer, List<RoleEntity>>)loginUser.getRoleList().parallelStream().
				collect(groupingBy(RoleEntity::getId));
		//req.setParentMenuIsNull(1);
		List<MenuEntity> list = menuDao.getListByHql(queryHql(req,loginUser), "");
		list.sort(comparingInt(MenuEntity::getOrderNum));
		list.forEach(o->{
			res.getMenuList().add(getMenuForm(o, null!=req.getDepth()? req.getDepth():20,collect,req));
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	
	public MenuForm getMenuForm(MenuEntity o,Integer depth,Map<Integer, List<RoleEntity>> roleMap,MenuReq req) {
		
		MenuForm form = new MenuForm();
		form.setMenuId(o.getId());
		form.setClientSide(o.getClientSide());
		form.setMenuName(o.getMenuName());
		form.setMenuType(o.getMenuType());
		form.setHref(o.getHref());
		form.setDescription(o.getDescription());
		form.setPath(o.getPath());
		form.setComponent(o.getComponent());
		form.setVisible(o.getVisible());
		form.setIsCache(o.getIsCache());
		form.setIsFrame(o.getIsFrame());
		form.setOrderNum(o.getOrderNum());
		form.setStatus(o.getStatus());
		form.setIcon(o.getIcon());
		form.setMenuCode(o.getMenuCode());
		form.setIsBindingPermission(null!=req.getPermissionId() ? 0 : null);
		form.setIsBindingRole(null!=req.getRoleId()? 0 : null);
		if(depth>0) {
			o.getSubMenus().sort(comparingInt(MenuEntity::getOrderNum));
			form.setSubMenus(getMenuByDepth(o.getSubMenus(), depth,roleMap,req));
		}
		List<PermissionForm> permissions = new ArrayList<PermissionForm>();
		o.getPermissionList().forEach(p->{
			PermissionForm f = new PermissionForm();
			if(null!=req.getPermissionId() && req.getPermissionId().toString().equals(p.getId().toString())) {
				form.setIsBindingPermission(1);
			}
			f.setId(p.getId());
			f.setPermissionName(p.getPermissionName());
			f.setDisplayName(p.getDisplayName());
			f.setDescription(StringUtils.isNotEmpty(p.getDescription()) ? p.getDescription() : "");
			//f.setHref(StringUtils.isNotEmpty(p.getHref()) ? p.getHref() : "");
			//f.setGroupName(o.getGroupName());
			permissions.add(f);
		});
		List<RoleForm> roleList = new ArrayList<RoleForm>();
		o.getRoleList().forEach(r->{
			if(null!=req.getRoleId() && req.getRoleId().toString().equals(r.getId().toString())) {
				form.setIsBindingRole(1);
			}
			RoleForm role = new RoleForm();
			role.setRoleId(r.getId());
			role.setRoleName(r.getRoleName());
			role.setBuiltIn(r.isBuiltIn());
			role.setDisplayName(r.getDisplayName());
			roleList.add(role);
		});
		form.setPermissionList(permissions);
		form.setRoleList(roleList);
		return form;
	}
	public String queryHql(MenuReq req,UserEntity loginUser) {
		StringBuilder hql = new StringBuilder("select distinct a from MenuEntity a ");
		StringBuilder roleId = new StringBuilder();
		for (RoleEntity role : loginUser.getRoleList()) {
			roleId.append(role.getId()+",");
		}
		hql.append(" inner join a.roleList b where  b.id in("+roleId.subSequence(0, roleId.length()-1)+")");
		hql.append(StringUtils.isNotEmpty(req.getMenuName()) ? " and a.menuName like'%"+req.getMenuName()+"%'":"")
			.append(StringUtils.isNotEmpty(req.getMenuTypeList()) ? " and a.menuName in("+req.getMenuTypeList()+")":"")
			.append(null!=req.getParentId() ? " and a.parentMenu.id="+req.getParentId() : " ")
			.append(null!=req.getStatus() ? " and a.status=" +req.getStatus(): "")
			.append(null!=req.getVisible() ?" and a.visible="+req.getVisible():"")
			.append(StringUtils.isNotEmpty(req.getMenuCode()) ? " and a.menuCode ='"+req.getMenuCode()+"'":"")
			.append(null!=req.getClientSide() ? " and a.clientSide=" +req.getClientSide(): "");
		if(null==req.getParentId() && null!=req.getParentMenuIsNull() && req.getParentMenuIsNull()==1 ){
			hql.append(null==req.getParentId() && null!=req.getParentMenuIsNull() && req.getParentMenuIsNull()==1 ? 
					" and a.parentMenu is null" :"")
				.append(" ORDER BY a.orderNum asc");
		}else {
			hql.append(" ORDER BY a.id desc");
		}
		
		return hql.toString();
	}
	@Override
	public IResponse getMenuList(MenuReq req) {
		GetMenuListRes res = new GetMenuListRes();
		UserEntity loginUser = getCurrentUser();
		if(null == loginUser) {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
			return res;
		}
		Map<Integer, List<RoleEntity>> collect = 
				(Map<Integer, List<RoleEntity>>)loginUser.getRoleList().parallelStream().
				collect(groupingBy(RoleEntity::getId));
		Page<MenuEntity> page = new Page<MenuEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);

		page = menuDao.queryPage(page, queryHql(req,loginUser));

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			res.getMenuList().add(getMenuForm(o, 0,collect, req));
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	@Override
	@Audit(operate = "角色绑定菜单")
	public IResponse roleBindingMenu(MenuReq req) {
		GenericResponse res = new GenericResponse();
		if (StringUtils.isNotEmpty(req.getMenuIdList()) && StringUtils.isNotEmpty(req.getRoleIdList())) {
			List<MenuEntity> list = new ArrayList<MenuEntity>();
			 String[] menuIds =
					 req.getMenuIdList().split(","); 
			 for (String menuId : menuIds) {
				 MenuEntity menu =
					 menuDao.get(Integer.parseInt(menuId)); 
				 if(null != menu) { 
					 list.add(menu); 
			 	} 
			 }
			String[] roleIds = req.getRoleIdList().split(",");
			for (String id : roleIds) {
				RoleEntity role = roleDao.get(Integer.valueOf(id));
				if (null != role) {
					role.getMenuList().clear();
					role.getPermissionList().clear();
					for (MenuEntity menu : list) {
						if (!role.getMenuList().contains(menu)) {
							role.getMenuList().add(menu);
						}
						for(PermissionEntity permission : menu.getPermissionList()) {//把这个菜单下的权限也加上
							if (!role.getPermissionList().contains(permission)) {
								role.getPermissionList().add(permission);
							}
						}
					}
				}
			}

			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}
	@Override
	@Audit(operate = "角色解绑菜单")
	public IResponse roleUnbindingMenu(MenuReq req) {
		GenericResponse res = new GenericResponse();
		if (StringUtils.isNotEmpty(req.getMenuIdList())
				&& StringUtils.isNotEmpty(req.getRoleIdList())) {
			List<MenuEntity> list = new ArrayList<MenuEntity>();
			 String[] menuIds =
					 req.getMenuIdList().split(","); 
			 for (String menuId : menuIds) {
				 MenuEntity menu =
					 menuDao.get(Integer.parseInt(menuId)); 
				 if(null != menu) { 
					 list.add(menu); 
			 	} 
			 }
			 String[] roleIds = req.getRoleIdList().split(",");
			 for (String id : roleIds) {
				RoleEntity role = roleDao.get(Integer.valueOf(id));
				if (null != role) {
					for (MenuEntity menu : list) {
						if (role.getMenuList().contains(menu)) {
							role.getMenuList().remove(menu);
						}
						for(PermissionEntity permission : menu.getPermissionList()) {
							boolean state = true;
							for(MenuEntity m : role.getMenuList()) {
								if(m.getPermissionList().contains(permission)) {
									state = false;
									break;
								}
							}
							if (state) {
								role.getPermissionList().remove(permission);
							}
						}
					}
				}
			 }

			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}
	
	
	
	@Override
	@Audit(operate = "菜单绑定权限")
	public IResponse menuBindingPermission(MenuReq req) {
		GenericResponse res = new GenericResponse();
		if (StringUtils.isNotEmpty(req.getMenuIdList()) && StringUtils.isNotEmpty(req.getPermissionIdList())) {
			List<PermissionEntity> list = new ArrayList<PermissionEntity>();
			 String[] permissionIds =
					 req.getPermissionIdList().split(","); 
			 for (String permissionId : permissionIds) {
				 PermissionEntity permission =
						 permissionDao.get(Integer.parseInt(permissionId)); 
				 if(null != permission) { 
					 list.add(permission); 
			 	} 
			 }
			String[] menuIds = req.getMenuIdList().split(",");
			for (String id : menuIds) {
				MenuEntity menu = menuDao.get(Integer.valueOf(id));
				menu.getPermissionList().clear();
				if (null != menu) {
					for (PermissionEntity permission : list) {
						if (!menu.getPermissionList().contains(permission)) {
							menu.getPermissionList().add(permission);
						}
					}
				}
			}

			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}
	@Override
	@Audit(operate = "菜单解绑权限")
	public IResponse menuUnbindingPermission(MenuReq req) {
		GenericResponse res = new GenericResponse();
		if (StringUtils.isNotEmpty(req.getMenuIdList())
				&& StringUtils.isNotEmpty(req.getPermissionIdList())) {
			List<PermissionEntity> list = new ArrayList<PermissionEntity>();
			 String[] permissionIds =
					 req.getPermissionIdList().split(","); 
			 for (String permissionId : permissionIds) {
				 PermissionEntity permission =
						 permissionDao.get(Integer.parseInt(permissionId)); 
				 if(null != permission) { 
					 list.add(permission); 
			 	} 
			 }
			 String[] menuIds = req.getMenuIdList().split(",");
			 for (String id : menuIds) {
				 MenuEntity menu = menuDao.get(Integer.parseInt(id)); 
				if (null != menu) {
					for (PermissionEntity permission : list) {
						if (menu.getPermissionList().contains(permission)) {
							menu.getPermissionList().remove(permission);
						}
					}
				}
			 }

			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	
	/**
	 * 根据用户ID查询菜单
	 * 
	 * @param userId 用户名称
	 * @return 菜单列表
	 */
	@Override
	public IResponse getRouterTree(MenuReq req) {
		GetRouterTreeRes res = new GetRouterTreeRes();
		List<MenuEntity> menuList = null;
		UserEntity user = getCurrentUser();
		if (null == user) {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
			return res;
		}

		// 获取全部菜单
		StringBuilder hql = new StringBuilder("select m from MenuEntity m where 1=1 ");
		hql.append(StringUtils.isNotEmpty(req.getMenuCode()) ? " and m.menuCode like '"+req.getMenuCode()+"%'":"")
			.append(null!=req.getClientSide() ? " and m.clientSide=" +req.getClientSide(): "");
		hql.append(" and m.status = 1 and m.menuType in (0,2) ORDER BY m.orderNum ASC");
		menuList = menuDao.getListByHql(hql.toString());

		// 根据用户过滤没有权限的菜单
		//List<MenuEntity> selectMenuByUser = selectMenuByUser(menuList, user);
		Map<String,MenuEntity> selectMenuByUser = selectMenuByUser(menuList, user);

		List<MenuEntity> childMenu = getChildMenus(selectMenuByUser, null!=req.getParentId() ? req.getParentId() :0);
		
		res.setRouterFormList(buildMenus(childMenu));
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		
		return res;
	}
	
	/**
	 * 根据用户过滤没有权限的菜单
	 * 
	 * @param permissions 菜单列表
	 * @param user  用户对晚用
	 * @return 过滤后的菜单
	 */
	private Map<String,MenuEntity> selectMenuByUser(List<MenuEntity> menuList, UserEntity user) {
		Map<String,MenuEntity> resultMenuMap = new HashMap<String,MenuEntity>();
		//List<MenuEntity> resultMenu = new ArrayList<MenuEntity>();
		Map<String, MenuEntity> userPermissionMap = new HashMap<String, MenuEntity>();
		for (RoleEntity role : user.getRoleList()) {
			role.getMenuList().forEach(o -> {
				userPermissionMap.put(o.getMenuCode(), o);
			});
		}

		menuList.forEach(m -> {
//			if (StringUtils.isEmpty(m.getMenuCode())) {
//				resultMenu.add(m);
//			} else {
				if (userPermissionMap.containsKey(m.getMenuCode()) ) {
					//resultMenu.add(m);
					resultMenuMap.put(m.getMenuCode(), m);
				}
			//}
		});

		//return resultMenu;
		return resultMenuMap;
	}
	
	/**
	 * 根据父节点的ID获取所有子节点，并重新复制
	 * 
	 * @param list     分类表
	 * @param parentId 传入的父节点ID
	 * @return String
	 */
	private List<MenuEntity> getChildMenus(Map<String,MenuEntity> map, int parentId) {
		List<MenuEntity> returnList = new ArrayList<MenuEntity>();
		//for (Iterator<MenuEntity> iterator = list.iterator(); iterator.hasNext();) {
		for(String key : map.keySet()) {
			//MenuEntity t = (MenuEntity) iterator.next();
			MenuEntity t = (MenuEntity) map.get(key);

			// 一、根据传入的某个父节点ID,遍历该父节点的所有子节点
			if ((t.getParentMenu() != null) && t.getParentMenu().getId().intValue() == parentId) {
				MenuEntity dstMenu = new MenuEntity(); 
				try {
					BeanUtils.copyProperties(dstMenu, t);
				} catch (IllegalAccessException e) {
					e.printStackTrace();
				} catch (InvocationTargetException e) {
					e.printStackTrace();
				}
				recursionFn(dstMenu.getSubMenus(), dstMenu,map);
				if(dstMenu.getSubMenus().size() >= 0) {
					returnList.add(dstMenu);
				}
				else {
					if((EntityContext.MENU_TYPE_MENU.equals(dstMenu.getMenuType()) 
					&&  StringUtils.isNotEmpty(dstMenu.getMenuCode()))) {
						returnList.add(dstMenu);
					}
				}
			}
		}
		return returnList;
	}

	/**
	 * 递归列表
	 * 
	 * @param list
	 * @param t
	 */
	private void recursionFn(List<MenuEntity> list, MenuEntity t,Map<String,MenuEntity> map) {
		// 得到子节点列表
		List<MenuEntity> childList = getChildList(list, t,map);
		t.setSubMenus(childList);
		for (MenuEntity tChild : childList) {
			if (hasChild(list, tChild,map)) {
				recursionFn(list, tChild,map);
			}
		}
	}
	/**
	 * 判断是否有子节点
	 */
	private boolean hasChild(List<MenuEntity> list, MenuEntity t,Map<String,MenuEntity> map) {
		return getChildList(list, t,map).size() > 0 ? true : false;
	}
	
	/**
	 * 得到子节点列表
	 */
	private List<MenuEntity> getChildList(List<MenuEntity> list, MenuEntity t,Map<String,MenuEntity> map) {
		List<MenuEntity> tlist = new ArrayList<MenuEntity>();
		Iterator<MenuEntity> it = list.iterator();
		while (it.hasNext()) {
			MenuEntity n = (MenuEntity) it.next();
			if (n.getParentMenu() != null && n.getParentMenu().getId().longValue() == t.getId().longValue()) {
				MenuEntity dstMenu = new MenuEntity(); 
				try {
					BeanUtils.copyProperties(dstMenu, n);
				} catch (IllegalAccessException e) {
					e.printStackTrace();
				} catch (InvocationTargetException e) {
					e.printStackTrace();
				}
				if(map.containsKey(dstMenu.getMenuCode())) {
					tlist.add(dstMenu);
				}
				
			}
		}
		return tlist;
	}
	/**
	 * 构建前端路由所需要的菜单
	 * 
	 * @param permissions 菜单列表
	 * @return 路由列表
	 */
	private List<RouterForm> buildMenus(List<MenuEntity> menus) {
		List<RouterForm> routers = new LinkedList<RouterForm>();
		for (MenuEntity menu : menus) {
			RouterForm router = new RouterForm();
			router.setHidden(0 == menu.getVisible());
			router.setName(getRouteName(menu));
			router.setPath(getRouterPath(menu));
			router.setComponent(getComponent(menu));
			router.setMeta(new MetaForm(menu.getMenuName(), menu.getIcon(), 0 == menu.getIsCache()));
			router.setOrderNum(null!=menu.getOrderNum()?menu.getOrderNum():0);
			List<MenuEntity> cPermissions = menu.getSubMenus();
			if (!cPermissions.isEmpty() && cPermissions.size() > 0 && EntityContext.MENU_TYPE_DIR.equals(menu.getMenuType())) {
				router.setAlwaysShow(true);
				router.setRedirect("noRedirect");
				router.setChildren(buildMenus(cPermissions));
			} else if (isMenuFrame(menu)) {
				router.setMeta(null);
				List<RouterForm> childrenList = new ArrayList<RouterForm>();
				RouterForm children = new RouterForm();
				children.setPath(menu.getPath());
				children.setComponent("@/views/"+menu.getComponent());
				children.setName(StringUtils.capitalize(menu.getPath()));
				children.setMeta(new MetaForm(menu.getMenuName(), menu.getIcon(), 0 == menu.getIsCache()));
				children.setOrderNum(null!=menu.getOrderNum()?menu.getOrderNum():0);
				childrenList.add(children);
				childrenList.sort(comparingInt(RouterForm::getOrderNum));
				router.setChildren(childrenList);
			}
			
			routers.add(router);
		}
		routers.sort(comparingInt(RouterForm::getOrderNum));
		return routers;
	}
	/**
	 * 获取路由名称
	 * 
	 * @param permission 菜单信息
	 * @return 路由名称
	 */
	private String getRouteName(MenuEntity menu) {
		String routerName = StringUtils.capitalize(menu.getPath());
		// 非外链并且是一级目录（类型为目录）
		if (isMenuFrame(menu)) {
			routerName = StringUtils.EMPTY;
		}
		return routerName;
	}

	/**
	 * 获取路由地址
	 * 
	 * @param permission 菜单信息
	 * @return 路由地址
	 */
	private String getRouterPath(MenuEntity menu) {
		String routerPath = menu.getPath();
		// 非外链并且是一级目录（类型为目录）
		if ( 0 == menu.getParentMenu().getId().intValue()
				&& EntityContext.MENU_TYPE_DIR.equals(menu.getMenuType())
				&& EntityContext.MENU_NO_FRAME.equals(menu.getIsFrame())) {
			routerPath = "/" + menu.getPath();
		}
		// 非外链并且是一级目录（类型为菜单）
		else if (isMenuFrame(menu)) {
			routerPath = "/";
		}
		return routerPath;
	}

	/**
	 * 获取组件信息
	 * 
	 * @param permission 菜单信息
	 * @return 组件信息
	 */
	private String getComponent(MenuEntity menu) {
		String component = EntityContext.ROUTER_LAYOUT;
		if (StringUtils.isNotEmpty(menu.getComponent()) && !isMenuFrame(menu)) {
			component = "@/views/" + menu.getComponent();
		} else if (StringUtils.isEmpty(menu.getComponent()) && isParentView(menu)) {
			component = EntityContext.ROUTER_PARENT_VIEW;
		}
		return component;
	}
	/**
	 * 是否为菜单内部跳转
	 * 
	 * @param permission 菜单信息
	 * @return 结果
	 */
	private boolean isMenuFrame(MenuEntity menu) {
		return menu.getParentMenu().getId() == 0
				&& EntityContext.MENU_TYPE_MENU.equals(menu.getMenuType()) 
				&& EntityContext.MENU_NO_FRAME.equals(menu.getIsFrame());
	}

	/**
	 * 是否为parent_view组件
	 * 
	 * @param permission 菜单信息
	 * @return 结果
	 */
	private boolean isParentView(MenuEntity menu) {
		return menu.getParentMenu().getId().intValue() != 0
				&& EntityContext.MENU_TYPE_DIR.equals(menu.getMenuType());
	}

}

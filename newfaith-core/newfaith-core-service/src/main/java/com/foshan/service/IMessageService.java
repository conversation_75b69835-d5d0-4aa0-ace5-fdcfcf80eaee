package com.foshan.service;

import com.foshan.form.request.MessageReq;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.msg.GetUnreadMessageCountRes;

public interface IMessageService {
	public IResponse readMessage(MessageReq req);
	public IResponse getMessageList(MessageReq req);
	public IResponse getUnreadCount(MessageReq req);
	public IResponse getMessageInfo(MessageReq req);
	public IResponse sendMessage(MessageReq req, Integer clientType);
	public IResponse clearUnReadCache(MessageReq req);
}

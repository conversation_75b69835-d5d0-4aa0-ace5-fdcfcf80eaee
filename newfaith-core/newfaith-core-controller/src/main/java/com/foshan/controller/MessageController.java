package com.foshan.controller;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.request.MessageReq;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.msg.GetUnreadMessageCountRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cache.Cache;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import com.foshan.form.response.msg.GetMessageListRes;
import com.foshan.form.response.msg.GetMessageRes;
import javax.servlet.http.HttpServletRequest;

@Api(tags = "融合平台--消息模块")
@RestController
public class MessageController extends BaseController {

	@ApiOperation(value = "获取消息列表(getMessageList)", httpMethod = "POST", notes = "获取当前会员或管理员的消息列表，按发送时间倒序排序，id、messageCode(模糊)、receiverType、receiverId、status、type、endTime、startTime")
	@ResponseBody
	@RequestMapping(value = "/getMessageList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetMessageListRes getMessageList(@RequestBody MessageReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetMessageListRes res =(GetMessageListRes)messageService.getMessageList(req);
		return res;
	}
	
	
	@ApiOperation(value = "获取消息详情(getMessageInfo)", httpMethod = "POST", notes = "获取消息详情，只能获取当前登录用户或会员的消息,id和meessageCode都存在的情况，优先使用id查询，id和meessageCode不能时时为空")
	@ResponseBody
	@RequestMapping(value = "/getMessageInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetMessageRes getMessageInfo(@RequestBody MessageReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetMessageRes res =(GetMessageRes)messageService.getMessageInfo(req);
		return res;
	}

	@ApiOperation(value = "只能获取当前登录用户或会员的未读消息(getUnreadCount)", httpMethod = "POST", notes = "")
	@ResponseBody
	@RequestMapping(value = "/getUnreadCount", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetUnreadMessageCountRes getUnreadCount(@RequestBody MessageReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetUnreadMessageCountRes res =(GetUnreadMessageCountRes)messageService.getUnreadCount(req);
		return res;
	}
	
	@ApiOperation(value = "阅读消息(readMessage)", httpMethod = "POST", notes = "获取当前登录用户或会员的未读消息")
	@ResponseBody
	@RequestMapping(value = "/readMessage", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse readMessage(@RequestBody MessageReq req, HttpServletRequest request)
			throws JsonProcessingException {
		
		return (GenericResponse)messageService.readMessage(req);
	}

//	@ApiOperation(value = "测试发送消息(sendMessage)", httpMethod = "POST", notes = "获取消息详情<p>1:id、messsageCode不能同时为空；")
//	@ResponseBody
//	@RequestMapping(value = "/sendMessage", method = {
//			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
//	public GenericResponse sendMessage(@RequestBody MessageReq req, HttpServletRequest request)
//			throws JsonProcessingException {
//		return (GenericResponse)messageService.sendMessage(req,0);
//	}

	@ApiOperation(value = "清理未读数据缓存(clearUnReadCache)", httpMethod = "POST", notes = "1、除超级管理员（userName = sqadm）外的所有用户或会员，只能清理自己的未读消息数量缓存，以修复缓存未读消息数量与数字库不一致的情况。\n" +
			"2、超缓管理员，可以清理任何用户或会员对应的未读消息数量缓存，如何receiverId和receiverType都为空情况下，可以清理所有会员或用户的未读消息数量缓存")
	@ResponseBody
	@RequestMapping(value = "/clearUnReadCache", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse clearUnReadCache(@RequestBody MessageReq req, HttpServletRequest request)
			throws JsonProcessingException {
		return (GenericResponse)messageService.clearUnReadCache(req);
	}

}

package com.foshan.service.cultureCloud.impl;

import java.io.File;
import java.io.IOException;

import java.text.SimpleDateFormat;
import java.util.Date;

import javax.servlet.http.HttpServletRequest;
import javax.transaction.Transactional;

import com.foshan.entity.AssetEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.entity.cultureCloud.CultureCloudUserEntity;
import com.foshan.service.impl.UploadServiceImpl;
import com.foshan.util.CodeUtil;
import com.foshan.util.DigestUtil;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;


import com.foshan.form.UploadForm;
import com.foshan.form.request.UploadReq;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.form.response.upload.UploadRes;
import com.foshan.service.annotation.Audit;
import com.foshan.service.cultureCloud.ICultureCloudUploadService;


@Transactional
@Service("cultureCloudUploadService")
public class CultureCloudUploadServiceImpl extends UploadServiceImpl implements ICultureCloudUploadService {

    @SuppressWarnings("rawtypes")
    @Audit(operate = "上传文化云文件")
    @Override
    public IResponse uploadCultureCloudLiveReplay(HttpServletRequest request, MultipartFile[] file, UploadReq req) throws IOException {
        UploadRes res = new UploadRes();
        req = getAssetSpecForm(req);

        Object userObj = getPrincipal(true);
        if (null == userObj && !(userObj instanceof CultureCloudUserEntity)) {
            res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO);
            return res;
        }

        for (MultipartFile tempFile : file) {
            try {
                UploadForm uploadForm = new UploadForm();
                uploadForm = disposeFile(tempFile, req, uploadForm);
                res.getUploadFormList().add(uploadForm);
            } catch (IOException e) {
                res.setRet("0003");
                res.setRetInfo("服务器异常！！！");
                e.printStackTrace();
                return res;
            }
        }
        res.setRet(ResponseContext.RES_SUCCESS_CODE);
        res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        return res;
    }

    public UploadForm disposeFile(MultipartFile file, UploadReq req, UploadForm uploadForm) throws IOException {
        String dir = "";
        String url = "";
        boolean isPic = false;
        Date date = new Date();
        Integer assetType = null;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
        String today = sdf.format(date);
        String suffix = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1).toLowerCase();
        if (suffix.equals("wmv") || suffix.equals("asf") || suffix.equals("asx") ||
                suffix.equals("rm") || suffix.equals("rmvb") || suffix.equals("mpg") ||
                suffix.equals("mpeg") || suffix.equals("mpe") || suffix.equals("mkv") ||
                suffix.equals("3gp") || suffix.equals("mov") || suffix.equals("mp4") ||
                suffix.equals("m4v") || suffix.equals("avi") || suffix.equals("ts")
        ) {
            dir = contextInfo.getAssetFilePath() + File.separator + "LiveReplay" + File.separator + today;
            url = contextInfo.getAssetFileUrl() + "/" + "LiveReplay" + "/" + today + "/";
            assetType = EntityContext.ASSET_TYPE_VIDEO;
        } else {
            return null;
        }
        createMultilayerFile(dir);
        if (file != null) {
            String transName = DigestUtil.getMD5Str(System.currentTimeMillis() + file.getOriginalFilename() + CodeUtil.getId(10000));
            String fileName = transName + file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));

            //避免有问题的图片造成机顶盒重启问题
            //modifyImageFormat(fileUpload,fileUpload,suffix);
            AssetEntity asset = null;
            if (null != req.getAssetId()) {
                asset = assetDao.get(req.getAssetId());
            } else {
                asset = new AssetEntity();
            }

            String fileUpload = dir + File.separator + fileName;
            File tempFile = new File(fileUpload);
            file.transferTo(tempFile);
            if (isOSLinux()) {
                Runtime.getRuntime().exec("chmod 644 " + fileUpload);
            }
            url = url + fileName;
            uploadForm.setFilePath(url);
            asset.setAssetState(EntityContext.ASSET_STATE_PENDING);
            asset.setImageFile(url);
            asset.setSmallImageFile(url);


            if (null != req.getParentAssetId()) {
                AssetEntity pae = assetDao.get(req.getParentAssetId());
                if (null != pae) {
                    asset.setParentAsset(pae);
                    if (isPic) {
                        asset.setIsCover(EntityContext.IS_NOT_COVER);
                    }
                } else if (isPic) {
                    asset.setIsCover(EntityContext.IS_COVER);
                }
            } else if (isPic) {
                asset.setIsCover(EntityContext.IS_COVER);
            }
            asset.setAssetCode(transName);
//			asset.setAssetName(new String(file.getOriginalFilename()
//					.getBytes("ISO-8859-1"),"UTF-8").split("[.]")[0]);
            asset.setAssetName(file.getOriginalFilename());
            asset.setAssetType(assetType);
            asset.setPackageFlag(EntityContext.ASSET_PACKAGE_FLAG_INVALID);
            asset.setSummaryShort(req.getSummaryShort());

            assetDao.saveOrUpdate(asset);
            uploadForm.setFileId(asset.getId());
            uploadForm.setFileName(file.getOriginalFilename());
        }
        return uploadForm;
    }
}
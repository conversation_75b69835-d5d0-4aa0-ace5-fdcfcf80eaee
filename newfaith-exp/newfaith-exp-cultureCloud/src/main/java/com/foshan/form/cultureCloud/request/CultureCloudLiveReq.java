package com.foshan.form.cultureCloud.request;

import com.foshan.form.request.BasePageRequest;
import com.foshan.form.request.GenericRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CultureCloudLiveReq extends BasePageRequest {
    
    @ApiModelProperty(value = "直播ID")
    private Integer id;
    
    @ApiModelProperty(value = "直播名称", required = true)
    private String liveTitle;
    
    @ApiModelProperty(value = "直播用户", required = true)
    private String liveCreateUser;
    
    @ApiModelProperty(value = "平台类型 1:本站 2:第三方平台", required = true)
    private Integer platformType;
    
    @ApiModelProperty(value = "PC链接")
    private String pcUrl;
    
    @ApiModelProperty(value = "H5链接")
    private String h5Url;
    
    @ApiModelProperty(value = "封面图片", required = true)
    private String liveCoverImg;
    
    @ApiModelProperty(value = "活动地点", required = true)
    private String liveAddress;
    
    @ApiModelProperty(value = "直播分类", required = true)
    private Integer liveCut;
    
    @ApiModelProperty(value = "开始时间", required = true)
    private String liveStartTimeStr;
    
    @ApiModelProperty(value = "结束时间", required = true)
    private String liveEndTimeStr;
    
    @ApiModelProperty(value = "直播来源 2:视频直播 3:第三方流")
    private Integer cloudService;
    
    @ApiModelProperty(value = "发布来源")
    private String publishSource;
    
    @ApiModelProperty(value = "所属部门ID")
    private Integer branchDeptId;
    
    @ApiModelProperty(value = "是否转码 0:否 1:是")
    private Integer liveIsTranscoding;
    
    @ApiModelProperty(value = "转码模板ID")
    private String transcodeTemplateId;
    
    @ApiModelProperty(value = "是否延迟 0:否 1:是")
    private Integer liveIsDelay;
    
    @ApiModelProperty(value = "延迟时间(秒)")
    private Integer liveDelayTime;
    
    @ApiModelProperty(value = "第三方流地址")
    private String liveVideoUrl;

    @ApiModelProperty(value = "直播回看视频地址")
    private String liveUploadLink;
    
    @ApiModelProperty(value = "固定评论文案")
    private String fixedCopy;
    
    @ApiModelProperty(value = "是否必须登录 0:否 1:是")
    private Integer mustLogin;
    
    @ApiModelProperty(value = "是否显示回看 0:否 1:是")
    private Integer useAutoRecord;
    
    @ApiModelProperty(value = "简介")
    private String liveDesc;
    
    @ApiModelProperty(value = "详情")
    private String liveIntroduction;
    
    @ApiModelProperty(value = "直播状态 0-未审核 1-草稿 2-已审核 3-个人发布活动待审核 5-回收站 6-已发布 7-个人审核未通过")
    private Integer liveStatus;
    
    @ApiModelProperty(value = "是否删除 0-删除 1-正常")
    private Integer isDelete;

    @ApiModelProperty(value = "主办单位")
    private String organizer;

    @ApiModelProperty(value = "执行单位")
    private String executor;

    @ApiModelProperty(value = "承办单位")
    private String undertaker;

    @ApiModelProperty(value = "协办单位")
    private String coOrganizer;

    @ApiModelProperty(value = "艺术类型ID,多个以英文逗号隔开")
    private String artTypeIdList;

    @ApiModelProperty(value = "直播活动时间状态 1:正在直播 2:尚未开始 3:已结束")
    private Integer liveActivityTimeStatus;

}
